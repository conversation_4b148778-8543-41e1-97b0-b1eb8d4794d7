<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فوركس الأردن - أفضل وسيط تداول في الشرق الأوسط</title>
    <meta name="description" content="انضم لأفضل وسيط فوركس في المنطقة العربية. حسابات إسلامية، بونص 50%، سبريد منخفض، تداول آمن ومضمون">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700;800;900&family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        :root {
            --primary-bg: #0a0e1a;
            --secondary-bg: #1a1f2e;
            --luxury-dark: #0d1421;
            --accent-color: #64ffda;
            --gold-primary: #FFD700;
            --gold-secondary: #FFA500;
            --gold-light: #FFEF94;
            --silver-primary: #C0C0C0;
            --silver-secondary: #E5E5E5;
            --platinum: #E5E4E2;
            --main-text: #ffffff;
            --secondary-text: #b8c5d6;
            --luxury-text: #f8f9fa;
            --card-bg: rgba(26, 31, 46, 0.8);
            --card-luxury: rgba(255, 255, 255, 0.05);
            --border-color: rgba(255, 215, 0, 0.3);
            --border-luxury: rgba(255, 255, 255, 0.1);
            --success-color: #4CAF50;
            --warning-color: #ffc107;
            --shadow-luxury: 0 25px 50px rgba(0, 0, 0, 0.5);
            --glow-gold: 0 0 30px rgba(255, 215, 0, 0.3);
            --glow-silver: 0 0 25px rgba(192, 192, 192, 0.2);
            --glow-accent: 0 0 35px rgba(100, 255, 218, 0.4);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(100, 255, 218, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(192, 192, 192, 0.05) 0%, transparent 50%),
                linear-gradient(135deg, var(--luxury-dark) 0%, var(--primary-bg) 50%, var(--secondary-bg) 100%);
            color: var(--main-text);
            line-height: 1.7;
            overflow-x: hidden;
            position: relative;
            font-weight: 400;
            letter-spacing: 0.5px;
        }

        /* Particles.js container */
        #particles-js {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 1;
            pointer-events: none;
        }

        /* العلامة المائية المتحركة */
        .watermark {
            position: fixed;
            top: 0;
            left: 0;
            font-size: 8rem;
            font-weight: 900;
            color: rgba(100, 255, 218, 0.04);
            z-index: 0;
            pointer-events: none;
            user-select: none;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            white-space: nowrap;
            animation: moveAround 25s ease-in-out infinite, shimmer 6s ease-in-out infinite;
            -webkit-text-stroke: 1px rgba(100, 255, 218, 0.02);
            filter: drop-shadow(0 0 20px rgba(100, 255, 218, 0.1));
            transform-origin: center;
        }

        @keyframes shimmer {
            0% { opacity: 0.02; text-shadow: 0 0 10px rgba(100, 255, 218, 0.1); }
            25% { opacity: 0.05; text-shadow: 0 0 20px rgba(100, 255, 218, 0.15); }
            50% { opacity: 0.08; text-shadow: 0 0 30px rgba(100, 255, 218, 0.2); }
            75% { opacity: 0.05; text-shadow: 0 0 20px rgba(100, 255, 218, 0.15); }
            100% { opacity: 0.02; text-shadow: 0 0 10px rgba(100, 255, 218, 0.1); }
        }

        @keyframes moveAround {
            0% { transform: translate(10vw, 10vh) rotate(-45deg) scale(0.8); opacity: 0.03; }
            12.5% { transform: translate(80vw, 15vh) rotate(-30deg) scale(1); opacity: 0.05; }
            25% { transform: translate(85vw, 70vh) rotate(-15deg) scale(1.1); opacity: 0.04; }
            37.5% { transform: translate(70vw, 85vh) rotate(0deg) scale(0.9); opacity: 0.06; }
            50% { transform: translate(15vw, 80vh) rotate(15deg) scale(1.2); opacity: 0.03; }
            62.5% { transform: translate(5vw, 60vh) rotate(30deg) scale(0.7); opacity: 0.05; }
            75% { transform: translate(20vw, 20vh) rotate(45deg) scale(1); opacity: 0.04; }
            87.5% { transform: translate(60vw, 10vh) rotate(60deg) scale(0.9); opacity: 0.06; }
            100% { transform: translate(10vw, 10vh) rotate(-45deg) scale(0.8); opacity: 0.03; }
        }

        /* الهيدر الفاخر */
        .navbar {
            background: linear-gradient(135deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(13, 20, 33, 0.98) 100%) !important;
            backdrop-filter: blur(25px);
            border-bottom: 2px solid transparent;
            border-image: linear-gradient(90deg,
                rgba(255, 215, 0, 0.3),
                rgba(100, 255, 218, 0.3),
                rgba(192, 192, 192, 0.3)) 1;
            padding: 20px 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: var(--shadow-luxury);
        }

        .navbar-brand {
            font-size: 2rem;
            font-weight: 900;
            background: linear-gradient(135deg,
                var(--gold-primary) 0%,
                var(--accent-color) 50%,
                var(--silver-primary) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-shadow: var(--glow-gold);
            letter-spacing: 1px;
        }

        .navbar-brand:hover {
            transform: scale(1.08) translateY(-2px);
            filter: drop-shadow(var(--glow-gold)) drop-shadow(var(--glow-accent));
            text-shadow:
                0 0 20px rgba(255, 215, 0, 0.6),
                0 0 40px rgba(100, 255, 218, 0.4);
        }

        /* قسم البطل الفاخر */
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            z-index: 10;
            padding-top: 120px;
            background:
                radial-gradient(circle at 30% 70%, rgba(255, 215, 0, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(100, 255, 218, 0.12) 0%, transparent 50%),
                linear-gradient(135deg, rgba(0, 0, 0, 0.85), rgba(13, 20, 33, 0.9)),
                url('https://images.unsplash.com/photo-1559526324-4b87b5e36e44?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80') center/cover;
            background-attachment: fixed;
        }

        .hero-content {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.08) 0%,
                rgba(255, 215, 0, 0.05) 25%,
                rgba(100, 255, 218, 0.08) 75%,
                rgba(192, 192, 192, 0.05) 100%);
            border-radius: 30px;
            border: 2px solid transparent;
            background-clip: padding-box;
            backdrop-filter: blur(30px);
            padding: 5rem;
            box-shadow:
                var(--shadow-luxury),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                0 0 60px rgba(255, 215, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .hero-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                transparent,
                rgba(255, 215, 0, 0.1),
                transparent,
                rgba(100, 255, 218, 0.1),
                transparent);
            animation: luxuryShimmer 4s ease-in-out infinite;
            pointer-events: none;
        }

        .hero-content::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg,
                var(--gold-primary),
                var(--accent-color),
                var(--silver-primary),
                var(--gold-secondary));
            border-radius: 32px;
            z-index: -1;
            animation: borderGlow 3s ease-in-out infinite alternate;
        }

        @keyframes luxuryShimmer {
            0% { transform: translateX(-100%) rotate(45deg); }
            100% { transform: translateX(200%) rotate(45deg); }
        }

        @keyframes borderGlow {
            0% { opacity: 0.5; }
            100% { opacity: 0.8; }
        }

        .hero-title {
            font-size: 4.5rem;
            font-weight: 900;
            background: linear-gradient(135deg,
                var(--gold-primary) 0%,
                var(--gold-light) 25%,
                var(--accent-color) 50%,
                var(--silver-primary) 75%,
                var(--platinum) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 2rem;
            text-shadow:
                0 0 40px rgba(255, 215, 0, 0.6),
                0 0 80px rgba(100, 255, 218, 0.4);
            position: relative;
            z-index: 2;
            letter-spacing: 2px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.6rem;
            color: var(--luxury-text);
            margin-bottom: 2.5rem;
            font-weight: 500;
            text-shadow:
                0 2px 15px rgba(0, 0, 0, 0.7),
                0 0 25px rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 2;
            line-height: 1.6;
            letter-spacing: 0.8px;
        }

        .countries-list {
            display: flex;
            flex-wrap: wrap;
            gap: 25px;
            margin: 3.5rem 0;
            justify-content: center;
            position: relative;
            z-index: 2;
        }

        .country-badge {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.12) 0%,
                rgba(255, 215, 0, 0.08) 50%,
                rgba(100, 255, 218, 0.1) 100%);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: var(--luxury-text);
            padding: 15px 25px;
            border-radius: 35px;
            font-weight: 700;
            font-size: 1.1rem;
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
            overflow: hidden;
            letter-spacing: 0.5px;
        }

        .country-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 215, 0, 0.2),
                transparent);
            transition: left 0.6s;
        }

        .country-badge:hover::before {
            left: 100%;
        }

        .country-badge:hover {
            transform: translateY(-8px) scale(1.08);
            box-shadow:
                0 20px 50px rgba(255, 215, 0, 0.3),
                0 0 30px rgba(100, 255, 218, 0.2);
            background: linear-gradient(135deg,
                rgba(255, 215, 0, 0.15) 0%,
                rgba(100, 255, 218, 0.12) 100%);
            border-color: var(--gold-primary);
        }

        .country-flag {
            width: 28px;
            height: 21px;
            border-radius: 4px;
            object-fit: cover;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* الأزرار الفاخرة */
        .btn-primary-custom {
            background: linear-gradient(135deg,
                var(--gold-primary) 0%,
                var(--gold-secondary) 25%,
                var(--accent-color) 75%,
                var(--silver-primary) 100%);
            border: none;
            color: var(--luxury-dark);
            font-weight: 900;
            padding: 22px 50px;
            border-radius: 60px;
            font-size: 1.4rem;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 20px 50px rgba(255, 215, 0, 0.4),
                0 0 30px rgba(100, 255, 218, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-family: 'Cairo', sans-serif;
        }

        .btn-primary-custom::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.4),
                transparent);
            transition: left 0.6s;
        }

        .btn-primary-custom::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            transition: all 0.6s;
            transform: translate(-50%, -50%);
        }

        .btn-primary-custom:hover::before {
            left: 100%;
        }

        .btn-primary-custom:hover::after {
            width: 300px;
            height: 300px;
        }

        .btn-primary-custom:hover {
            transform: translateY(-8px) scale(1.08);
            box-shadow:
                0 30px 70px rgba(255, 215, 0, 0.6),
                0 0 50px rgba(100, 255, 218, 0.4),
                0 0 100px rgba(255, 255, 255, 0.1);
            background: linear-gradient(135deg,
                var(--gold-light) 0%,
                var(--accent-color) 50%,
                var(--platinum) 100%);
            color: var(--luxury-dark);
        }

        .btn-secondary-custom {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(100, 255, 218, 0.1) 100%);
            backdrop-filter: blur(15px);
            border: 2px solid transparent;
            background-clip: padding-box;
            color: var(--luxury-text);
            font-weight: 700;
            padding: 18px 40px;
            border-radius: 60px;
            font-size: 1.2rem;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
            letter-spacing: 1px;
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .btn-secondary-custom::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg,
                var(--accent-color),
                var(--gold-primary),
                var(--silver-primary));
            border-radius: 62px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.5s;
        }

        .btn-secondary-custom:hover::before {
            opacity: 1;
        }

        .btn-secondary-custom:hover {
            background: linear-gradient(135deg,
                rgba(100, 255, 218, 0.15) 0%,
                rgba(255, 215, 0, 0.1) 100%);
            color: var(--luxury-text);
            transform: translateY(-5px) scale(1.05);
            box-shadow:
                0 15px 40px rgba(100, 255, 218, 0.4),
                0 0 30px rgba(255, 215, 0, 0.2);
        }

        /* البطاقات الفاخرة */
        .feature-card {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.08) 0%,
                rgba(255, 215, 0, 0.03) 25%,
                rgba(100, 255, 218, 0.05) 75%,
                rgba(192, 192, 192, 0.04) 100%);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 25px;
            backdrop-filter: blur(25px);
            padding: 3rem;
            text-align: center;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 20px 50px rgba(0, 0, 0, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            height: 100%;
            position: relative;
            z-index: 10;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 215, 0, 0.1) 0%,
                transparent 50%,
                rgba(100, 255, 218, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.6s ease;
        }

        .feature-card::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg,
                var(--gold-primary),
                var(--accent-color),
                var(--silver-primary),
                var(--gold-secondary));
            border-radius: 27px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.6s ease;
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-card:hover::after {
            opacity: 0.7;
        }

        .feature-card:hover {
            border-color: transparent;
            box-shadow:
                0 35px 80px rgba(255, 215, 0, 0.3),
                0 0 50px rgba(100, 255, 218, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transform: translateY(-20px) scale(1.05);
            background: linear-gradient(135deg,
                rgba(255, 215, 0, 0.12) 0%,
                rgba(100, 255, 218, 0.1) 100%);
        }

        .feature-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            display: inline-block;
        }

        .feature-icon i {
            position: relative;
            z-index: 2;
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 80px;
            height: 80px;
            background: radial-gradient(circle,
                rgba(255, 215, 0, 0.1) 0%,
                rgba(100, 255, 218, 0.05) 50%,
                transparent 100%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
            transition: all 0.6s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.2) rotateY(10deg);
            filter: drop-shadow(0 0 25px rgba(255, 215, 0, 0.4));
        }

        .feature-card:hover .feature-icon::before {
            width: 120px;
            height: 120px;
            background: radial-gradient(circle,
                rgba(255, 215, 0, 0.2) 0%,
                rgba(100, 255, 218, 0.1) 50%,
                transparent 100%);
        }

        .feature-title {
            font-size: 1.8rem;
            font-weight: 800;
            background: linear-gradient(135deg,
                var(--luxury-text) 0%,
                var(--gold-light) 50%,
                var(--luxury-text) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1.5rem;
            letter-spacing: 0.5px;
            line-height: 1.3;
        }

        .feature-description {
            color: var(--secondary-text);
            font-size: 1.1rem;
            line-height: 1.8;
            font-weight: 400;
            letter-spacing: 0.3px;
        }

        /* قسم المميزات الخاصة */
        .special-features {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(100, 255, 218, 0.05));
            border-radius: 20px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            padding: 3rem;
            margin: 3rem 0;
            position: relative;
            z-index: 10;
        }

        .special-feature-item {
            display: flex;
            align-items: center;
            margin: 1rem 0;
            padding: 1rem;
            background: rgba(255, 215, 0, 0.1);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .special-feature-item:hover {
            background: rgba(255, 215, 0, 0.2);
            transform: translateX(10px);
        }

        .special-feature-icon {
            font-size: 1.5rem;
            color: var(--gold-color);
            margin-left: 15px;
            min-width: 40px;
        }

        /* تحسينات للهواتف */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-content {
                padding: 2rem;
            }
            
            .countries-list {
                justify-content: center;
            }
            
            .watermark {
                font-size: 4rem;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .btn-primary-custom {
                padding: 12px 25px;
                font-size: 1rem;
            }
            
            .watermark {
                font-size: 2.5rem;
            }
        }

        /* تأثيرات إضافية */
        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(100, 255, 218, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(100, 255, 218, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(100, 255, 218, 0);
            }
        }

        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {
            0% { transform: translate(0, 0px); }
            50% { transform: translate(0, -10px); }
            100% { transform: translate(0, 0px); }
        }

        .section-title {
            font-size: 3rem;
            font-weight: 900;
            background: linear-gradient(135deg,
                var(--gold-primary) 0%,
                var(--accent-color) 50%,
                var(--silver-primary) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 4rem;
            text-shadow:
                0 0 30px rgba(255, 215, 0, 0.4),
                0 0 60px rgba(100, 255, 218, 0.3);
            letter-spacing: 1px;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg,
                var(--gold-primary),
                var(--accent-color),
                var(--silver-primary));
            transform: translateX(-50%);
            border-radius: 2px;
        }

        .highlight-text {
            background: linear-gradient(135deg,
                var(--gold-primary) 0%,
                var(--gold-light) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 800;
            text-shadow: var(--glow-gold);
        }

        .success-rate {
            background: linear-gradient(135deg,
                var(--success-color) 0%,
                #66BB6A 50%,
                var(--gold-secondary) 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 35px;
            font-weight: 800;
            display: inline-block;
            margin: 15px 0;
            box-shadow:
                0 10px 25px rgba(76, 175, 80, 0.4),
                0 0 20px rgba(255, 215, 0, 0.2);
            font-size: 1.1rem;
            letter-spacing: 0.5px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* تأثيرات إضافية فاخرة */
        .luxury-glow {
            position: relative;
        }

        .luxury-glow::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg,
                var(--gold-primary),
                var(--accent-color),
                var(--silver-primary),
                var(--gold-secondary));
            border-radius: inherit;
            z-index: -1;
            filter: blur(10px);
            opacity: 0;
            transition: opacity 0.6s ease;
        }

        .luxury-glow:hover::before {
            opacity: 0.7;
        }

        /* تحسينات للهواتف */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 3rem;
            }

            .hero-content {
                padding: 3rem;
            }

            .section-title {
                font-size: 2.2rem;
            }

            .btn-primary-custom {
                padding: 18px 35px;
                font-size: 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-content {
                padding: 2rem;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .btn-primary-custom {
                padding: 15px 25px;
                font-size: 1rem;
            }

            .feature-card {
                padding: 2rem;
            }
        }
    </style>
</head>

<body>
    <!-- Particles.js Background -->
    <div id="particles-js"></div>

    <!-- العلامة المائية المتحركة -->
    <div class="watermark">فوركس الأردن</div>
    <div class="watermark" style="animation-delay: -5s; opacity: 0.02;">فوركس الأردن</div>
    <div class="watermark" style="animation-delay: -10s; opacity: 0.015;">فوركس الأردن</div>

    <!-- الهيدر -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand animate__animated animate__fadeInDown" href="#home">
                <i class="fas fa-chart-line" style="margin-left: 8px;"></i>
                فوركس الأردن
            </a>
            
            <div class="d-flex align-items-center">
                <a href="#contact" class="btn btn-outline-primary me-3 animate__animated animate__fadeInDown animate__delay-1s">
                    <i class="fas fa-phone" style="margin-left: 5px;"></i>
                    تواصل معنا
                </a>
            </div>
        </div>
    </nav>

    <!-- قسم البطل (Hero) -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-12">
                    <div class="hero-content text-center" data-aos="fade-up" data-aos-duration="1000">
                        <h1 class="hero-title animate__animated animate__fadeInUp">
                            <i class="fas fa-chart-line" style="color: var(--gold-color); margin-left: 15px; font-size: 0.8em;"></i>
                            فوركس الأردن
                            <i class="fas fa-crown" style="color: var(--gold-color); margin-right: 15px; font-size: 0.8em;"></i>
                        </h1>
                        <p class="hero-subtitle animate__animated animate__fadeInUp animate__delay-1s">
                            <i class="fas fa-trophy" style="color: var(--gold-color); margin-left: 8px;"></i>
                            أفضل وسيط تداول في الشرق الأوسط - شركة زيل كابيتال ماركت من TOP 10 بالفوركس
                        </p>

                        <div class="countries-list animate__animated animate__fadeInUp animate__delay-2s">
                            <span class="country-badge">
                                <img src="https://flagcdn.com/w40/jo.png" alt="الأردن" class="country-flag">
                                الأردن
                            </span>
                            <span class="country-badge">
                                <img src="https://flagcdn.com/w40/sy.png" alt="سوريا" class="country-flag">
                                سوريا
                            </span>
                            <span class="country-badge">
                                <img src="https://flagcdn.com/w40/ly.png" alt="ليبيا" class="country-flag">
                                ليبيا
                            </span>
                            <span class="country-badge">
                                <img src="https://flagcdn.com/w40/iq.png" alt="العراق" class="country-flag">
                                العراق
                            </span>
                            <span class="country-badge">
                                <img src="https://flagcdn.com/w40/ye.png" alt="اليمن" class="country-flag">
                                اليمن
                            </span>
                            <span class="country-badge">
                                <img src="https://flagcdn.com/w40/ps.png" alt="فلسطين" class="country-flag">
                                فلسطين
                            </span>
                            <span class="country-badge">
                                <img src="https://flagcdn.com/w40/lb.png" alt="لبنان" class="country-flag">
                                لبنان
                            </span>
                            <span class="country-badge">
                                <img src="https://flagcdn.com/w40/eg.png" alt="مصر" class="country-flag">
                                مصر
                            </span>
                        </div>

                        <div class="mb-4 animate__animated animate__fadeInUp animate__delay-3s">
                            <div class="success-rate">
                                <i class="fas fa-chart-line" style="margin-left: 5px;"></i>
                                نسبة نجاح 80%+ حسب نتائج مايو
                            </div>
                        </div>

                        <div class="d-flex flex-column flex-md-row gap-3 justify-content-center animate__animated animate__fadeInUp animate__delay-4s">
                            <a href="https://my.zfx-asia.com/reg/truely?agentnumber=Z2918566C1"
                               class="btn-primary-custom pulse-animation"
                               target="_blank">
                                <i class="fas fa-rocket" style="margin-left: 8px;"></i>
                                ابدأ التداول الآن - بونص 50%
                            </a>
                            <a href="#features" class="btn-secondary-custom">
                                <i class="fas fa-info-circle" style="margin-left: 8px;"></i>
                                اعرف المزيد
                            </a>
                        </div>

                        <div class="mt-4 animate__animated animate__fadeInUp animate__delay-5s">
                            <p style="color: var(--secondary-text); font-size: 0.9rem;">
                                <i class="fas fa-shield-alt" style="color: var(--success-color); margin-left: 5px;"></i>
                                تداول آمن ومضمون - شركة مرخصة ومعتمدة عالمياً
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم المميزات الرئيسية -->
    <section id="features" class="py-5">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">
                <i class="fas fa-star" style="color: var(--gold-color); margin-left: 10px;"></i>
                لماذا زيل كابيتال ماركت؟
            </h2>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-card floating">
                        <div class="feature-icon">
                            <i class="fas fa-mosque" style="background: linear-gradient(45deg, var(--success-color), #66BB6A); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h3 class="feature-title">حسابات إسلامية 100%</h3>
                        <p class="feature-description">
                            أفضل حساب إسلامي في الفوركس على الإطلاق - افتح صفقة لأشهر أو سنوات بدون فوائد ربوية
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card floating" style="animation-delay: 0.5s;">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar" style="background: linear-gradient(45deg, var(--accent-color), #57cbff); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h3 class="feature-title">أقل سبريد</h3>
                        <p class="feature-description">
                            أقل سبريد وبدون عمولات - يصل أقل من نقطة واحدة مع تصنيف ECN المتقدم
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card floating" style="animation-delay: 1s;">
                        <div class="feature-icon">
                            <i class="fas fa-gift" style="background: linear-gradient(45deg, var(--gold-color), #FFA726); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h3 class="feature-title">بونص 50%</h3>
                        <p class="feature-description">
                            بونص ترحيبي 50% على الإيداع الأول - ابدأ بضعف رأس مالك
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-card floating" style="animation-delay: 1.5s;">
                        <div class="feature-icon">
                            <i class="fas fa-rocket" style="background: linear-gradient(45deg, #FF6B6B, #FF8E53); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h3 class="feature-title">تنفيذ سريع</h3>
                        <p class="feature-description">
                            تنفيذ فوري للصفقات - تدعم كل أنواع التداول مع مانع انزلاقات الأسعار خلال الأخبار
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-card floating" style="animation-delay: 2s;">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line" style="background: linear-gradient(45deg, #9C27B0, #E91E63); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h3 class="feature-title">رافعة مالية عالية</h3>
                        <p class="feature-description">
                            رافعة مالية تصل حتى 1:2000 - تداول بمبالغ أكبر برأس مال أقل
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
                    <div class="feature-card floating" style="animation-delay: 2.5s;">
                        <div class="feature-icon">
                            <i class="fas fa-credit-card" style="background: linear-gradient(45deg, #2196F3, #00BCD4); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h3 class="feature-title">طرق دفع متنوعة</h3>
                        <p class="feature-description">
                            إيداع وسحب بكل الطرق (بنوك - محافظ - كريبتو) + بطاقة Gate To Pay
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم المميزات الخاصة -->
    <section class="py-5">
        <div class="container">
            <div class="special-features" data-aos="fade-up">
                <h2 class="section-title">
                    <i class="fas fa-crown" style="color: var(--gold-color); margin-left: 10px;"></i>
                    مميزات حصرية لعملاء فوركس الأردن
                </h2>

                <div class="row">
                    <div class="col-lg-6" data-aos="fade-right" data-aos-delay="100">
                        <div class="special-feature-item">
                            <div class="special-feature-icon">
                                <i class="fas fa-id-card"></i>
                            </div>
                            <div>
                                <h5 style="color: var(--main-text); margin-bottom: 5px;">تفعيل مبسط</h5>
                                <p style="color: var(--secondary-text); margin: 0;">التفعيل بالهوية فقط لمشتركي فوركس الأردن</p>
                            </div>
                        </div>

                        <div class="special-feature-item">
                            <div class="special-feature-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div>
                                <h5 style="color: var(--main-text); margin-bottom: 5px;">تداول آمن</h5>
                                <p style="color: var(--secondary-text); margin: 0;">شركة مثالية ومرخصة من أوائل الشركات المعتمدة عالمياً</p>
                            </div>
                        </div>

                        <div class="special-feature-item">
                            <div class="special-feature-icon">
                                <i class="fas fa-ban"></i>
                            </div>
                            <div>
                                <h5 style="color: var(--main-text); margin-bottom: 5px;">مانع الانزلاق</h5>
                                <p style="color: var(--secondary-text); margin: 0;">مانع انزلاقات الأسعار خلال الأخبار المهمة</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6" data-aos="fade-left" data-aos-delay="200">
                        <div class="special-feature-item">
                            <div class="special-feature-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div>
                                <h5 style="color: var(--main-text); margin-bottom: 5px;">تصنيف ECN</h5>
                                <p style="color: var(--secondary-text); margin: 0;">تصنيف ECN - مزود سيولة من الدرجة الأولى</p>
                            </div>
                        </div>

                        <div class="special-feature-item">
                            <div class="special-feature-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <h5 style="color: var(--main-text); margin-bottom: 5px;">صفقات طويلة المدى</h5>
                                <p style="color: var(--secondary-text); margin: 0;">افتح صفقة لأشهر أو سنوات بدون قيود زمنية</p>
                            </div>
                        </div>

                        <div class="special-feature-item">
                            <div class="special-feature-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div>
                                <h5 style="color: var(--main-text); margin-bottom: 5px;">TOP 10 عالمياً</h5>
                                <p style="color: var(--secondary-text); margin: 0;">من أفضل 10 شركات فوركس على مستوى العالم</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم خدمات فوركس الأردن -->
    <section class="py-5">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">
                <i class="fas fa-bullseye" style="color: var(--accent-color); margin-left: 10px;"></i>
                خدمات قناة فوركس الأردن
            </h2>

            <div class="row g-4">
                <div class="col-lg-3 col-md-6" data-aos="zoom-in" data-aos-delay="100">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <h4 class="feature-title">توصيات دقيقة</h4>
                        <p class="feature-description">
                            توصيات يومية دقيقة للفوركس والذهب والمؤشرات مع نقاط دخول وخروج محسوبة
                        </p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6" data-aos="zoom-in" data-aos-delay="200">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-area"></i>
                        </div>
                        <h4 class="feature-title">تحليل فني</h4>
                        <p class="feature-description">
                            تحليل فني مبسط وسهل الفهم حتى للمبتدئين مع شرح مفصل للاستراتيجيات
                        </p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6" data-aos="zoom-in" data-aos-delay="300">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h4 class="feature-title">دعم مباشر</h4>
                        <p class="feature-description">
                            دعم مباشر من فريق تداول محترف متاح للإجابة على جميع استفساراتك
                        </p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6" data-aos="zoom-in" data-aos-delay="400">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-stop-circle"></i>
                        </div>
                        <h4 class="feature-title">إدارة المخاطر</h4>
                        <p class="feature-description">
                            نقاط وقف خسارة محسوبة بدقة لحماية رأس مالك وتحقيق أفضل النتائج
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم آراء العملاء -->
    <section class="py-5">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">
                <i class="fas fa-users" style="color: var(--success-color); margin-left: 10px;"></i>
                آراء عملائنا
            </h2>

            <div class="row g-4">
                <div class="col-lg-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-card">
                        <div class="mb-3">
                            <div style="color: var(--gold-color); font-size: 1.2rem;">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <p class="feature-description" style="font-style: italic;">
                            "أفضل وسيط تعاملت معه، الحساب الإسلامي ممتاز والسبريد منخفض جداً. التوصيات دقيقة ومربحة."
                        </p>
                        <h5 style="color: var(--accent-color); margin-top: 1rem;">أحمد محمد - الأردن</h5>
                    </div>
                </div>

                <div class="col-lg-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card">
                        <div class="mb-3">
                            <div style="color: var(--gold-color); font-size: 1.2rem;">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <p class="feature-description" style="font-style: italic;">
                            "البونص 50% ساعدني كثيراً في البداية، والتنفيذ سريع جداً. فريق الدعم متعاون ومحترف."
                        </p>
                        <h5 style="color: var(--accent-color); margin-top: 1rem;">فاطمة علي - مصر</h5>
                    </div>
                </div>

                <div class="col-lg-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card">
                        <div class="mb-3">
                            <div style="color: var(--gold-color); font-size: 1.2rem;">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <p class="feature-description" style="font-style: italic;">
                            "نسبة نجاح التوصيات عالية جداً، حققت أرباح ممتازة. التحليل الفني واضح ومفهوم."
                        </p>
                        <h5 style="color: var(--accent-color); margin-top: 1rem;">محمد حسن - سوريا</h5>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم CTA النهائي -->
    <section class="py-5">
        <div class="container">
            <div class="hero-content text-center" data-aos="zoom-in">
                <h2 style="font-size: 2.5rem; font-weight: 900; color: var(--accent-color); margin-bottom: 1rem;">
                    <i class="fas fa-rocket" style="margin-left: 10px;"></i>
                    لا تفوّت الفرصة!
                </h2>
                <p style="font-size: 1.3rem; color: var(--main-text); margin-bottom: 2rem;">
                    أماكن محدودة بالسعر الحالي - انضم الآن واحصل على بونص 50%
                </p>

                <div class="row justify-content-center mb-4">
                    <div class="col-md-8">
                        <div class="special-features" style="margin: 0;">
                            <div class="row text-center">
                                <div class="col-md-4 mb-3">
                                    <div class="highlight-text" style="font-size: 2rem; font-weight: 900;">80%+</div>
                                    <p style="color: var(--secondary-text); margin: 0;">نسبة النجاح</p>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="highlight-text" style="font-size: 2rem; font-weight: 900;">50%</div>
                                    <p style="color: var(--secondary-text); margin: 0;">بونص ترحيبي</p>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="highlight-text" style="font-size: 2rem; font-weight: 900;">1:2000</div>
                                    <p style="color: var(--secondary-text); margin: 0;">رافعة مالية</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center mb-4">
                    <a href="https://my.zfx-asia.com/reg/truely?agentnumber=Z2918566C1"
                       class="btn-primary-custom pulse-animation"
                       target="_blank"
                       style="font-size: 1.3rem; padding: 18px 45px;">
                        <i class="fas fa-user-plus" style="margin-left: 8px;"></i>
                        سجل الآن واحصل على البونص
                    </a>
                    <a href="#contact" class="btn-secondary-custom" style="font-size: 1.1rem; padding: 15px 35px;">
                        <i class="fas fa-phone" style="margin-left: 8px;"></i>
                        تواصل للاستفسار
                    </a>
                </div>

                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div style="background: rgba(76, 175, 80, 0.1); border: 1px solid var(--success-color); border-radius: 15px; padding: 1.5rem;">
                            <h5 style="color: var(--success-color); margin-bottom: 1rem;">
                                <i class="fas fa-clock" style="margin-left: 5px;"></i>
                                عرض محدود الوقت
                            </h5>
                            <p style="color: var(--main-text); margin: 0;">
                                احصل على بونص 50% + حساب إسلامي مجاني + دعم شخصي من فريق فوركس الأردن
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم التواصل -->
    <section id="contact" class="py-5" style="background: rgba(17, 34, 64, 0.3);">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">
                <i class="fas fa-phone" style="color: var(--accent-color); margin-left: 10px;"></i>
                تواصل معنا
            </h2>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="feature-card text-center" data-aos="fade-up">
                        <h4 style="color: var(--accent-color); margin-bottom: 2rem;">
                            فريق فوركس الأردن في خدمتك 24/7
                        </h4>

                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center justify-content-center mb-3">
                                    <i class="fas fa-telegram" style="font-size: 2rem; color: var(--accent-color); margin-left: 10px;"></i>
                                    <div>
                                        <h6 style="color: var(--main-text); margin: 0;">قناة التليجرام</h6>
                                        <p style="color: var(--secondary-text); margin: 0; font-size: 0.9rem;">للتوصيات والتحليلات</p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="d-flex align-items-center justify-content-center mb-3">
                                    <i class="fas fa-whatsapp" style="font-size: 2rem; color: var(--success-color); margin-left: 10px;"></i>
                                    <div>
                                        <h6 style="color: var(--main-text); margin: 0;">واتساب</h6>
                                        <p style="color: var(--secondary-text); margin: 0; font-size: 0.9rem;">للدعم المباشر</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <a href="https://my.zfx-asia.com/reg/truely?agentnumber=Z2918566C1"
                               class="btn-primary-custom"
                               target="_blank">
                                <i class="fas fa-external-link-alt" style="margin-left: 8px;"></i>
                                ابدأ التسجيل الآن
                            </a>
                        </div>

                        <div class="mt-4" style="border-top: 1px solid var(--border-color); padding-top: 1.5rem;">
                            <p style="color: var(--secondary-text); font-size: 0.9rem; margin: 0;">
                                <i class="fas fa-info-circle" style="margin-left: 5px;"></i>
                                التداول ينطوي على مخاطر. يرجى التداول بحذر ومسؤولية.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الفوتر -->
    <footer class="py-4" style="background: var(--primary-bg); border-top: 1px solid var(--border-color);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-chart-line" style="color: var(--accent-color); font-size: 1.5rem; margin-left: 10px;"></i>
                        <h5 style="color: var(--accent-color); margin: 0; font-weight: 700;">فوركس الأردن</h5>
                    </div>
                    <p style="color: var(--secondary-text); margin: 0; font-size: 0.9rem;">
                        شريكك الموثوق في رحلة التداول
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p style="color: var(--secondary-text); margin: 0; font-size: 0.9rem;">
                        © 2024 فوركس الأردن. جميع الحقوق محفوظة.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>

    <script>
        // تهيئة Particles.js
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 80,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": "#64ffda"
                },
                "shape": {
                    "type": "circle",
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    }
                },
                "opacity": {
                    "value": 0.5,
                    "random": false,
                    "anim": {
                        "enable": false,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 3,
                    "random": true,
                    "anim": {
                        "enable": false,
                        "speed": 40,
                        "size_min": 0.1,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#64ffda",
                    "opacity": 0.4,
                    "width": 1
                },
                "move": {
                    "enable": true,
                    "speed": 6,
                    "direction": "none",
                    "random": false,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": false,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "repulse"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "grab": {
                        "distance": 400,
                        "line_linked": {
                            "opacity": 1
                        }
                    },
                    "bubble": {
                        "distance": 400,
                        "size": 40,
                        "duration": 2,
                        "opacity": 8,
                        "speed": 3
                    },
                    "repulse": {
                        "distance": 200,
                        "duration": 0.4
                    },
                    "push": {
                        "particles_nb": 4
                    },
                    "remove": {
                        "particles_nb": 2
                    }
                }
            },
            "retina_detect": true
        });

        // تهيئة AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // تأثير التمرير السلس
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // تأثير الهيدر عند التمرير
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(10, 25, 47, 0.98)';
                navbar.style.boxShadow = '0 5px 20px rgba(0, 0, 0, 0.3)';
            } else {
                navbar.style.background = 'rgba(10, 25, 47, 0.95)';
                navbar.style.boxShadow = 'none';
            }
        });

        // تتبع النقرات على أزرار CTA
        document.querySelectorAll('a[href*="zfx-asia.com"]').forEach(button => {
            button.addEventListener('click', function() {
                // يمكن إضافة تتبع Google Analytics هنا
                console.log('CTA clicked: ', this.textContent);
            });
        });
    </script>

</body>
</html>
