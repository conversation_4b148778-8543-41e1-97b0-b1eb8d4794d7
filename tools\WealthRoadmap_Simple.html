<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خريطة الثراء - فوركس الأردن</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700;800;900&family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-bg: #0a192f;
            --secondary-bg: #112240;
            --accent-color: #64ffda;
            --main-text: #e6f1ff;
            --secondary-text: #8892b0;
            --card-bg: rgba(17, 34, 64, 0.6);
            --border-color: rgba(100, 255, 218, 0.2);
            --success-color: #4CAF50;
        }

        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #0a192f 0%, #112240 50%, #1e3a8a 100%);
            color: var(--main-text);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .navbar {
            background: rgba(10, 25, 47, 0.95) !important;
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(100, 255, 218, 0.2);
            padding: 15px 0;
        }

        .navbar-brand {
            font-size: 1.8rem;
            font-weight: 900;
            color: #64ffda !important;
            text-decoration: none;
        }

        .stage-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .stage-card:hover {
            border-color: rgba(100, 255, 218, 0.5);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
            transform: translateY(-5px);
        }

        .stage-card.completed {
            border-color: var(--success-color);
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), var(--card-bg));
        }

        .stage-card.active {
            border-color: var(--accent-color);
            background: linear-gradient(135deg, rgba(100, 255, 218, 0.1), var(--card-bg));
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); }
            50% { box-shadow: 0 20px 50px rgba(100, 255, 218, 0.3); }
            100% { box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); }
        }

        .stage-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .stage-card:hover .stage-icon {
            transform: scale(1.1);
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--accent-color), #57cbff);
            transition: width 0.5s ease;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--accent-color), #57cbff);
            border: none;
            color: var(--primary-bg);
            font-weight: 700;
            padding: 10px 25px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            background: linear-gradient(45deg, #57cbff, var(--accent-color));
        }

        .btn-success {
            background: linear-gradient(45deg, var(--success-color), #66BB6A);
            border: none;
            color: white;
            font-weight: 600;
        }

        .arrow {
            position: absolute;
            top: 50%;
            right: -30px;
            transform: translateY(-50%);
            z-index: 5;
        }

        .arrow svg {
            animation: arrowMove 2s ease-in-out infinite;
        }

        @keyframes arrowMove {
            0%, 100% { transform: translateX(0); opacity: 0.7; }
            50% { transform: translateX(10px); opacity: 1; }
        }

        .task-item {
            transition: all 0.3s ease;
            padding: 8px 12px;
            border-radius: 8px;
            margin: 5px 0;
        }

        .task-item:hover {
            background: rgba(100, 255, 218, 0.1);
        }

        .task-item.completed {
            background: rgba(76, 175, 80, 0.1);
            text-decoration: line-through;
            opacity: 0.7;
        }

        @media (max-width: 768px) {
            .arrow {
                display: none;
            }
        }
    </style>
</head>

<body>
    <!-- الهيدر -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../fxjordan.html">
                <i class="fas fa-chart-line" style="margin-left: 8px;"></i>
                فوركس الأردن
            </a>
            
            <div class="d-flex align-items-center">
                <a href="../fxjordan.html" class="btn btn-outline-primary me-3">
                    <i class="fas fa-home" style="margin-left: 5px;"></i>
                    الصفحة الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid" style="padding-top: 100px;">
        <!-- الشريط العلوي -->
        <div class="text-center mb-5">
            <div class="p-4" style="background: linear-gradient(135deg, rgba(100, 255, 218, 0.1), rgba(87, 203, 255, 0.05)); border-radius: 20px; border: 1px solid var(--border-color);">
                <h1 class="display-4 fw-bold mb-3" style="color: var(--accent-color);">
                    <i class="fas fa-map-marked-alt" style="margin-left: 15px;"></i>
                    خريطة الثراء
                </h1>
                <p class="lead mb-4" style="color: var(--main-text);">
                    ابدأ رحلتك نحو الحرية المالية من خلال 9 مراحل متدرجة ومدروسة
                </p>
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="d-flex justify-content-between align-items-center p-3" 
                             style="background: rgba(10, 25, 47, 0.7); border-radius: 15px; border: 1px solid var(--border-color);">
                            <div class="text-center">
                                <h5 style="color: var(--accent-color); margin-bottom: 5px;">التقدم الإجمالي</h5>
                                <h3 id="overallProgress" style="color: var(--main-text); font-weight: 700;">0%</h3>
                            </div>
                            <div class="flex-grow-1 mx-4">
                                <div class="progress" style="height: 15px; background: rgba(100, 255, 218, 0.1); border-radius: 10px;">
                                    <div id="overallProgressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="text-center">
                                <h5 style="color: var(--accent-color); margin-bottom: 5px;">المراحل المكتملة</h5>
                                <h3 id="completedStages" style="color: var(--main-text); font-weight: 700;">0/9</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- خريطة الثراء -->
        <div class="container">
            <div class="row" id="roadmapContainer">
                <!-- المراحل ستُضاف هنا بواسطة JavaScript -->
            </div>
        </div>

        <!-- مودال المهام -->
        <div class="modal fade" id="tasksModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content" style="background: var(--card-bg); border: 1px solid var(--border-color); border-radius: 15px;">
                    <div class="modal-header" style="border-bottom: 1px solid var(--border-color);">
                        <h5 class="modal-title" style="color: var(--accent-color);">
                            <span id="modalStageIcon"></span>
                            <span id="modalStageTitle"></span>
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" style="filter: invert(1);"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-4">
                            <h6 style="color: var(--main-text); margin-bottom: 10px;">وصف المرحلة:</h6>
                            <p id="modalStageDescription" style="color: var(--secondary-text);"></p>
                        </div>

                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 style="color: var(--main-text);">تقدم المرحلة:</h6>
                                <span id="modalStageProgress" style="color: var(--accent-color); font-weight: 600;">0%</span>
                            </div>
                            <div class="progress" style="height: 10px; background: rgba(100, 255, 218, 0.1); border-radius: 5px;">
                                <div id="modalProgressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>

                        <div>
                            <h6 style="color: var(--main-text); margin-bottom: 15px;">المهام المطلوبة:</h6>
                            <div id="tasksList">
                                <!-- المهام ستُضاف هنا -->
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer" style="border-top: 1px solid var(--border-color);">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-success" id="completeStageBtn" style="display: none;">
                            <i class="fas fa-check" style="margin-left: 5px;"></i>
                            إكمال المرحلة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // بيانات المراحل
        const stages = [
            {
                id: 1,
                title: "البداية",
                icon: "🚀",
                description: "أول خطوة في رحلة التداول - فهم الأساسيات والاستعداد النفسي",
                tasks: [
                    "قراءة كتاب عن أساسيات التداول",
                    "فهم مفهوم الفوركس والأسواق المالية",
                    "تحديد الأهداف المالية الشخصية",
                    "إعداد مساحة عمل مناسبة للتداول",
                    "تعلم المصطلحات الأساسية"
                ]
            },
            {
                id: 2,
                title: "فهم النفس",
                icon: "🧠",
                description: "تطوير العقلية الصحيحة للتداول وإدارة المشاعر",
                tasks: [
                    "تحليل نقاط القوة والضعف الشخصية",
                    "تعلم تقنيات إدارة التوتر",
                    "وضع قواعد نفسية للتداول",
                    "ممارسة التأمل أو تقنيات الاسترخاء",
                    "قراءة عن علم النفس في التداول"
                ]
            },
            {
                id: 3,
                title: "التداول المنضبط",
                icon: "📋",
                description: "تعلم الانضباط وإتباع القواعد في التداول",
                tasks: [
                    "وضع خطة تداول مكتوبة",
                    "تحديد قواعد الدخول والخروج",
                    "ممارسة التداول على حساب تجريبي",
                    "تسجيل جميع الصفقات في مذكرة",
                    "مراجعة الأداء أسبوعياً"
                ]
            },
            {
                id: 4,
                title: "إدارة المال الذكية",
                icon: "💰",
                description: "تعلم كيفية إدارة رأس المال وتقليل المخاطر",
                tasks: [
                    "تعلم قاعدة 1-2% مخاطرة لكل صفقة",
                    "حساب حجم اللوت المناسب",
                    "وضع استراتيجية لتوزيع المخاطر",
                    "تعلم متى تتوقف عن التداول",
                    "إنشاء صندوق طوارئ منفصل"
                ]
            },
            {
                id: 5,
                title: "احتراف التوقيت",
                icon: "⏰",
                description: "إتقان فن التوقيت في دخول وخروج الصفقات",
                tasks: [
                    "تعلم قراءة الشموع اليابانية",
                    "فهم مستويات الدعم والمقاومة",
                    "تطبيق المؤشرات الفنية الأساسية",
                    "تحليل الاتجاهات والأنماط",
                    "ممارسة التحليل اليومي للأسواق"
                ]
            },
            {
                id: 6,
                title: "التداول الحقيقي",
                icon: "💼",
                description: "الانتقال للتداول بأموال حقيقية بحذر وحكمة",
                tasks: [
                    "فتح حساب تداول حقيقي صغير",
                    "تطبيق استراتيجية التداول المختبرة",
                    "البدء بمبالغ صغيرة جداً",
                    "مراقبة الأداء النفسي تحت الضغط",
                    "تسجيل كل صفقة وتحليلها"
                ]
            },
            {
                id: 7,
                title: "الربح المستمر",
                icon: "📈",
                description: "تحقيق الربحية المستمرة وبناء الثقة",
                tasks: [
                    "تحقيق 3 أشهر متتالية من الربحية",
                    "زيادة حجم التداول تدريجياً",
                    "تطوير استراتيجيات متعددة",
                    "بناء شبكة من المتداولين المحترفين",
                    "الاستثمار في التعليم المستمر"
                ]
            },
            {
                id: 8,
                title: "نحو الحرية",
                icon: "🎯",
                description: "التحضير للاستقلال المالي الكامل",
                tasks: [
                    "تحقيق دخل شهري ثابت من التداول",
                    "تنويع مصادر الدخل",
                    "بناء محفظة استثمارية متوازنة",
                    "تعلم الاستثمار طويل المدى",
                    "وضع خطة للتقاعد المبكر"
                ]
            },
            {
                id: 9,
                title: "الحرية المالية",
                icon: "👑",
                description: "تحقيق الهدف النهائي - الحرية المالية الكاملة",
                tasks: [
                    "تحقيق الاستقلال المالي الكامل",
                    "بناء مصادر دخل سلبية",
                    "مساعدة الآخرين في رحلتهم",
                    "الاستمتاع بثمار الجهد والصبر",
                    "وضع إرث مالي للأجيال القادمة"
                ]
            }
        ];

        // متغيرات عامة
        let progressData = JSON.parse(localStorage.getItem('wealthRoadmapProgress')) || {};

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة');
            renderStages();
            updateOverallProgress();
        });

        // رسم المراحل
        function renderStages() {
            const container = document.getElementById('roadmapContainer');
            container.innerHTML = '';

            stages.forEach((stage, index) => {
                const stageProgress = getStageProgress(stage.id);
                const isCompleted = stageProgress === 100;
                const isActive = !isCompleted && (index === 0 || getStageProgress(stages[index - 1]?.id || 0) === 100);

                const col = document.createElement('div');
                col.className = 'col-lg-4 col-md-6 mb-4 position-relative';

                let cardClass = 'stage-card h-100 p-4 text-center';
                if (isCompleted) cardClass += ' completed';
                if (isActive) cardClass += ' active';

                col.innerHTML = `
                    <div class="${cardClass}">
                        <div class="stage-icon">${stage.icon}</div>
                        <h4 class="mb-3" style="color: var(--accent-color); font-weight: 700;">${stage.title}</h4>
                        <p class="mb-4" style="color: var(--secondary-text); font-size: 0.9rem;">${stage.description}</p>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small style="color: var(--main-text);">التقدم</small>
                                <small style="color: var(--accent-color); font-weight: 600;">${stageProgress}%</small>
                            </div>
                            <div class="progress" style="height: 8px; background: rgba(100, 255, 218, 0.1); border-radius: 4px;">
                                <div class="progress-bar" role="progressbar" style="width: ${stageProgress}%"></div>
                            </div>
                        </div>

                        <button class="btn ${isCompleted ? 'btn-success' : 'btn-primary'} w-100"
                                onclick="openStageModal(${stage.id})"
                                ${!isActive && !isCompleted ? 'disabled' : ''}>
                            <i class="fas ${isCompleted ? 'fa-check' : 'fa-play'}" style="margin-left: 5px;"></i>
                            ${isCompleted ? 'مكتملة' : isActive ? 'ابدأ' : 'مقفلة'}
                        </button>
                    </div>

                    ${index < stages.length - 1 ? `
                        <div class="arrow d-none d-lg-block">
                            <svg width="40" height="20" viewBox="0 0 40 20" fill="none">
                                <path d="M0 10 L30 10 M25 5 L30 10 L25 15" 
                                      stroke="#64ffda" 
                                      stroke-width="2" 
                                      stroke-linecap="round" 
                                      stroke-linejoin="round"/>
                            </svg>
                        </div>
                    ` : ''}
                `;

                container.appendChild(col);
            });
        }

        // الحصول على تقدم المرحلة
        function getStageProgress(stageId) {
            const stage = stages.find(s => s.id === stageId);
            if (!stage || !progressData[stageId]) return 0;

            const completedTasks = stage.tasks.filter((task, index) => 
                progressData[stageId] && progressData[stageId][`task-${index}`]
            ).length;

            return Math.round((completedTasks / stage.tasks.length) * 100);
        }

        // تحديث التقدم الإجمالي
        function updateOverallProgress() {
            const totalStages = stages.length;
            const completedStages = stages.filter(stage => getStageProgress(stage.id) === 100).length;
            const overallProgress = Math.round((completedStages / totalStages) * 100);

            document.getElementById('overallProgress').textContent = overallProgress + '%';
            document.getElementById('overallProgressBar').style.width = overallProgress + '%';
            document.getElementById('completedStages').textContent = `${completedStages}/${totalStages}`;

            // احتفال عند الإكمال الكامل
            if (overallProgress === 100) {
                showFinalCelebration();
            }
        }

        // فتح مودال المرحلة
        function openStageModal(stageId) {
            const stage = stages.find(s => s.id === stageId);
            if (!stage) return;

            const progress = getStageProgress(stageId);

            // تحديث محتوى المودال
            document.getElementById('modalStageIcon').textContent = stage.icon;
            document.getElementById('modalStageTitle').textContent = stage.title;
            document.getElementById('modalStageDescription').textContent = stage.description;
            document.getElementById('modalStageProgress').textContent = progress + '%';
            document.getElementById('modalProgressBar').style.width = progress + '%';

            // رسم المهام
            renderTasks(stage);

            // إظهار/إخفاء زر الإكمال
            const completeBtn = document.getElementById('completeStageBtn');
            if (progress === 100) {
                completeBtn.style.display = 'none';
            } else {
                completeBtn.style.display = 'inline-block';
                completeBtn.onclick = () => completeStage(stageId);
            }

            // فتح المودال
            const modal = new bootstrap.Modal(document.getElementById('tasksModal'));
            modal.show();
        }

        // رسم المهام
        function renderTasks(stage) {
            const tasksList = document.getElementById('tasksList');
            tasksList.innerHTML = '';

            stage.tasks.forEach((task, index) => {
                const isCompleted = isTaskCompleted(stage.id, index);

                const taskDiv = document.createElement('div');
                taskDiv.className = `task-item ${isCompleted ? 'completed' : ''}`;

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.className = 'form-check-input';
                checkbox.checked = isCompleted;
                checkbox.addEventListener('change', () => toggleTask(stage.id, index));

                const label = document.createElement('label');
                label.className = 'form-check-label';
                label.style.cssText = 'color: var(--main-text); cursor: pointer; margin-right: 10px;';
                label.textContent = task;

                const formCheck = document.createElement('div');
                formCheck.className = 'form-check';
                formCheck.appendChild(checkbox);
                formCheck.appendChild(label);

                taskDiv.appendChild(formCheck);
                tasksList.appendChild(taskDiv);
            });
        }

        // فحص إذا كانت المهمة مكتملة
        function isTaskCompleted(stageId, taskIndex) {
            return progressData[stageId] && progressData[stageId][`task-${taskIndex}`];
        }

        // تبديل حالة المهمة
        function toggleTask(stageId, taskIndex) {
            if (!progressData[stageId]) {
                progressData[stageId] = {};
            }

            const taskId = `task-${taskIndex}`;
            progressData[stageId][taskId] = !progressData[stageId][taskId];

            // حفظ التقدم
            localStorage.setItem('wealthRoadmapProgress', JSON.stringify(progressData));

            // تحديث العرض
            const progress = getStageProgress(stageId);
            document.getElementById('modalStageProgress').textContent = progress + '%';
            document.getElementById('modalProgressBar').style.width = progress + '%';

            // إظهار/إخفاء زر الإكمال
            const completeBtn = document.getElementById('completeStageBtn');
            if (progress === 100) {
                completeBtn.style.display = 'none';
                showCompletionNotification(stages.find(s => s.id === stageId).title);
            } else {
                completeBtn.style.display = 'inline-block';
            }

            // إعادة رسم المراحل
            renderStages();
            updateOverallProgress();
        }

        // إكمال المرحلة
        function completeStage(stageId) {
            const stage = stages.find(s => s.id === stageId);
            if (!stage) return;

            // تحديد جميع المهام كمكتملة
            if (!progressData[stageId]) {
                progressData[stageId] = {};
            }

            stage.tasks.forEach((task, index) => {
                progressData[stageId][`task-${index}`] = true;
            });

            // حفظ التقدم
            localStorage.setItem('wealthRoadmapProgress', JSON.stringify(progressData));

            // إغلاق المودال
            const modal = bootstrap.Modal.getInstance(document.getElementById('tasksModal'));
            modal.hide();

            // إظهار إشعار الإكمال
            showCompletionNotification(stage.title);

            // تحديث العرض
            renderStages();
            updateOverallProgress();
        }

        // إظهار إشعار الإكمال
        function showCompletionNotification(stageTitle) {
            const notification = document.createElement('div');
            notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
            notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 350px; border: 2px solid var(--success-color); background: linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(76, 175, 80, 0.7)); backdrop-filter: blur(10px);';
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-trophy" style="font-size: 2rem; color: #FFD700; margin-left: 15px;"></i>
                    <div>
                        <h6 class="mb-1" style="color: white; font-weight: 700;">🎉 تم اجتياز المرحلة!</h6>
                        <p class="mb-0" style="color: white; font-size: 0.9rem;">${stageTitle}</p>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" style="filter: invert(1);"></button>
            `;

            document.body.appendChild(notification);

            // إزالة تلقائية بعد 5 ثوان
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // احتفال الإكمال النهائي
        function showFinalCelebration() {
            const celebration = document.createElement('div');
            celebration.className = 'position-fixed w-100 h-100 d-flex align-items-center justify-content-center';
            celebration.style.cssText = 'top: 0; left: 0; z-index: 10000; background: rgba(0, 0, 0, 0.8); backdrop-filter: blur(5px);';
            celebration.innerHTML = `
                <div class="text-center p-5" style="background: linear-gradient(135deg, var(--card-bg), rgba(100, 255, 218, 0.1)); border-radius: 20px; border: 2px solid var(--accent-color); max-width: 500px;">
                    <div style="font-size: 5rem; margin-bottom: 20px;">🎉👑🎉</div>
                    <h1 style="color: var(--accent-color); font-weight: 900; margin-bottom: 20px;">مبروك!</h1>
                    <h3 style="color: var(--main-text); margin-bottom: 20px;">لقد أكملت خريطة الثراء بالكامل!</h3>
                    <p style="color: var(--secondary-text); margin-bottom: 30px;">
                        أنت الآن على الطريق الصحيح نحو الحرية المالية. استمر في تطبيق ما تعلمته وستحقق أهدافك.
                    </p>
                    <button class="btn btn-primary btn-lg" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-rocket" style="margin-left: 10px;"></i>
                        ابدأ رحلة النجاح
                    </button>
                </div>
            `;

            document.body.appendChild(celebration);
        }
    </script>

</body>
</html>
