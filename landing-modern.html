<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فوركس الأردن - أفضل وسيط تداول في الشرق الأوسط</title>
    <meta name="description" content="انضم لأفضل وسيط فوركس في المنطقة العربية. حسابات إسلامية، بونص 50%، سبريد منخفض">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        :root {
            --primary-color: #1a1a2e;
            --secondary-color: #16213e;
            --accent-color: #0f3460;
            --gold-color: #ffd700;
            --green-color: #00ff88;
            --blue-color: #00d4ff;
            --text-light: #ffffff;
            --text-gray: #b8b8b8;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-gold: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            --gradient-green: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            --gradient-blue: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Poppins', sans-serif;
            background: var(--primary-color);
            color: var(--text-light);
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* Particles Background */
        #particles-js {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 1;
        }

        /* Navigation */
        .navbar {
            background: rgba(26, 26, 46, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 215, 0, 0.2);
            padding: 20px 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            padding: 15px 0;
            background: rgba(26, 26, 46, 0.98) !important;
            box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
        }

        .navbar-brand {
            font-size: 2rem;
            font-weight: 900;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
        }

        .nav-link {
            color: var(--text-light) !important;
            font-weight: 600;
            margin: 0 15px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            color: var(--gold-color) !important;
            transform: translateY(-2px);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 50%;
            background: var(--gradient-gold);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Hero Section */
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            z-index: 10;
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.8) 0%, rgba(22, 33, 62, 0.9) 100%);
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            z-index: 10;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 900;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
        }

        .hero-subtitle {
            font-size: 1.5rem;
            color: var(--text-gray);
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .hero-description {
            font-size: 1.2rem;
            color: var(--text-light);
            margin-bottom: 3rem;
            line-height: 1.8;
        }

        /* Buttons */
        .btn-primary-modern {
            background: var(--gradient-gold);
            border: none;
            color: var(--primary-color);
            font-weight: 700;
            padding: 18px 40px;
            border-radius: 50px;
            font-size: 1.2rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn-primary-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary-modern:hover::before {
            left: 100%;
        }

        .btn-primary-modern:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 215, 0, 0.4);
            color: var(--primary-color);
        }

        .btn-secondary-modern {
            background: transparent;
            border: 2px solid var(--blue-color);
            color: var(--blue-color);
            font-weight: 600;
            padding: 16px 35px;
            border-radius: 50px;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-secondary-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: var(--gradient-blue);
            transition: width 0.3s ease;
            z-index: -1;
        }

        .btn-secondary-modern:hover::before {
            width: 100%;
        }

        .btn-secondary-modern:hover {
            color: var(--text-light);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
        }

        /* Stats Section */
        .stats-section {
            background: rgba(22, 33, 62, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            margin: 4rem 0;
            border: 1px solid rgba(255, 215, 0, 0.2);
            position: relative;
            z-index: 10;
        }

        .stat-item {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .stat-item:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .stat-label {
            font-size: 1.1rem;
            color: var(--text-gray);
            font-weight: 600;
        }

        /* Feature Cards */
        .feature-card-modern {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .feature-card-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(0, 212, 255, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card-modern:hover::before {
            opacity: 1;
        }

        .feature-card-modern:hover {
            transform: translateY(-15px);
            border-color: rgba(255, 215, 0, 0.3);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .feature-icon-modern {
            font-size: 4rem;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .feature-card-modern:hover .feature-icon-modern {
            transform: scale(1.1) rotateY(360deg);
        }

        .feature-title-modern {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-light);
            margin-bottom: 1rem;
        }

        .feature-description-modern {
            color: var(--text-gray);
            font-size: 1rem;
            line-height: 1.6;
        }

        /* Countries Section */
        .countries-section {
            background: rgba(15, 52, 96, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            margin: 4rem 0;
            border: 1px solid rgba(0, 255, 136, 0.2);
            position: relative;
            z-index: 10;
        }

        .country-flag {
            font-size: 3rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .country-item {
            text-align: center;
            padding: 2rem 1rem;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .country-item:hover {
            background: rgba(0, 255, 136, 0.1);
            transform: translateY(-5px);
        }

        .country-item:hover .country-flag {
            transform: scale(1.2);
        }

        .country-name {
            font-weight: 600;
            color: var(--text-light);
        }

        /* Section Titles */
        .section-title-modern {
            font-size: 3rem;
            font-weight: 900;
            text-align: center;
            margin-bottom: 3rem;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.2rem;
            }
            
            .section-title-modern {
                font-size: 2rem;
            }
            
            .btn-primary-modern,
            .btn-secondary-modern {
                padding: 15px 30px;
                font-size: 1rem;
            }
        }

        /* Animations */
        .floating {
            animation: floating 6s ease-in-out infinite;
        }

        @keyframes floating {
            0% { transform: translate(0, 0px); }
            50% { transform: translate(0, -20px); }
            100% { transform: translate(0, 0px); }
        }

        .pulse-glow {
            animation: pulse-glow 2s infinite;
        }

        @keyframes pulse-glow {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);
            }
            70% {
                box-shadow: 0 0 0 20px rgba(255, 215, 0, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
            }
        }
    </style>
</head>

<body>
    <!-- Particles Background -->
    <div id="particles-js"></div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#home">
                <i class="fas fa-chart-line me-2"></i>
                فوركس الأردن
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">المميزات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#countries">الدول</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">تواصل</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
