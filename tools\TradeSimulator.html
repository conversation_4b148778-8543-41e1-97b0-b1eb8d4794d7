<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محاكي التداول الوهمي - فوركس الأردن</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700;800;900&family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-bg: #0a192f;
            --secondary-bg: #112240;
            --accent-color: #64ffda;
            --main-text: #e6f1ff;
            --secondary-text: #8892b0;
            --card-bg: rgba(17, 34, 64, 0.6);
            --border-color: rgba(100, 255, 218, 0.2);
            --success-color: #4CAF50;
            --danger-color: #f44336;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #0a192f 0%, #112240 50%, #1e3a8a 100%);
            color: var(--main-text);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* تحسين التنقل السلس */
        a[href^="#"] {
            scroll-behavior: smooth;
        }

        /* تصميم الهيدر الموحد */
        .navbar {
            background: rgba(10, 25, 47, 0.95) !important;
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(100, 255, 218, 0.2);
            padding: 15px 0;
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-size: 1.8rem;
            font-weight: 900;
            color: #64ffda !important;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 0 10px rgba(100, 255, 218, 0.5));
        }

        .nav-link {
            color: #ccd6f6 !important;
            position: relative;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 12px 18px !important;
            border-radius: 8px;
            margin: 0 5px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 5px;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, #64ffda, #57cbff);
            transition: all 0.4s ease;
            transform: translateX(-50%);
            border-radius: 2px;
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 85%;
        }

        .nav-link:hover {
            color: #64ffda !important;
            transform: translateY(-3px);
            background: rgba(100, 255, 218, 0.1);
            box-shadow: 0 5px 15px rgba(100, 255, 218, 0.2);
        }

        .nav-link.active {
            color: #64ffda !important;
        }

        /* تصميم القائمة المنسدلة للأدوات */
        .dropdown-menu {
            border: none !important;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6) !important;
            background: rgba(10, 25, 47, 0.95) !important;
            backdrop-filter: blur(15px) !important;
            border-radius: 10px !important;
            border: 1px solid rgba(100, 255, 218, 0.3) !important;
        }

        .dropdown-item {
            border: none !important;
            background: transparent !important;
            color: #ccd6f6 !important;
            transition: all 0.3s ease !important;
            border-radius: 6px !important;
            margin: 2px 8px !important;
        }

        .dropdown-item:hover {
            background: rgba(100, 255, 218, 0.1) !important;
            color: #64ffda !important;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(100, 255, 218, 0.1) !important;
        }

        .dropdown-item:hover i {
            color: #64ffda !important;
            transform: scale(1.1);
            filter: drop-shadow(0 0 5px rgba(100, 255, 218, 0.5));
        }

        .dropdown-toggle::after {
            border-top: 4px solid;
            border-right: 3px solid transparent;
            border-left: 3px solid transparent;
            margin-right: 3px;
        }

        /* تأثيرات انتقالية للقائمة المنسدلة */
        .dropdown-menu {
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            display: block !important;
            visibility: hidden;
        }

        .dropdown:hover .dropdown-menu,
        .dropdown-menu.show {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        /* تحسينات للهواتف - الهيدر */
        @media (max-width: 768px) {
            .dropdown-menu {
                position: static !important;
                transform: none !important;
                opacity: 1 !important;
                visibility: visible !important;
                background: rgba(10, 25, 47, 0.98) !important;
                border-radius: 8px !important;
                margin: 5px 0 !important;
            }
            
            .dropdown-item {
                padding: 12px 20px !important;
                font-size: 0.9rem !important;
            }
        }

        /* تصميم البطاقات */
        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 10;
        }

        .card:hover {
            border-color: rgba(100, 255, 218, 0.5);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
            transform: translateY(-5px);
        }

        .card-header {
            background: rgba(100, 255, 218, 0.1);
            border-bottom: 1px solid var(--border-color);
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }

        .card-body {
            padding: 30px;
        }

        /* تصميم النماذج */
        .form-control,
        .form-select {
            background: rgba(10, 25, 47, 0.7);
            border: 1px solid var(--border-color);
            color: var(--main-text);
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus,
        .form-select:focus {
            background: rgba(10, 25, 47, 0.9);
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(100, 255, 218, 0.25);
            color: var(--main-text);
        }

        .form-control:hover,
        .form-select:hover {
            border-color: rgba(100, 255, 218, 0.4);
            box-shadow: 0 2px 8px rgba(100, 255, 218, 0.1);
        }

        .form-label {
            color: var(--main-text);
            font-weight: 600;
            margin-bottom: 8px;
        }

        /* تصميم الأزرار */
        .btn-primary {
            background: linear-gradient(45deg, var(--accent-color), #57cbff);
            border: none;
            color: var(--primary-bg);
            font-weight: 700;
            padding: 12px 30px;
            border-radius: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(100, 255, 218, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(100, 255, 218, 0.4);
            background: linear-gradient(45deg, #57cbff, var(--accent-color));
        }

        .btn-success {
            background: linear-gradient(45deg, var(--success-color), #66BB6A);
            border: none;
            color: white;
            font-weight: 600;
        }

        .btn-danger {
            background: linear-gradient(45deg, var(--danger-color), #EF5350);
            border: none;
            color: white;
            font-weight: 600;
        }

        /* العلامة المائية المتحركة */
        .watermark {
            position: fixed;
            top: 0;
            left: 0;
            font-size: 8rem;
            font-weight: 900;
            color: rgba(100, 255, 218, 0.04);
            z-index: 0;
            pointer-events: none;
            user-select: none;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            white-space: nowrap;
            animation: moveAround 25s ease-in-out infinite, shimmer 6s ease-in-out infinite;
            -webkit-text-stroke: 1px rgba(100, 255, 218, 0.02);
            filter: drop-shadow(0 0 20px rgba(100, 255, 218, 0.1));
            transform-origin: center;
        }

        /* تأثير الوميض/الشيمر */
        @keyframes shimmer {
            0% {
                opacity: 0.02;
                text-shadow: 0 0 10px rgba(100, 255, 218, 0.1);
            }
            25% {
                opacity: 0.05;
                text-shadow: 0 0 20px rgba(100, 255, 218, 0.15);
            }
            50% {
                opacity: 0.08;
                text-shadow: 0 0 30px rgba(100, 255, 218, 0.2);
            }
            75% {
                opacity: 0.05;
                text-shadow: 0 0 20px rgba(100, 255, 218, 0.15);
            }
            100% {
                opacity: 0.02;
                text-shadow: 0 0 10px rgba(100, 255, 218, 0.1);
            }
        }

        /* حركة العلامة المائية في جميع أنحاء الشاشة */
        @keyframes moveAround {
            0% {
                transform: translate(10vw, 10vh) rotate(-45deg) scale(0.8);
                opacity: 0.03;
            }
            12.5% {
                transform: translate(80vw, 15vh) rotate(-30deg) scale(1);
                opacity: 0.05;
            }
            25% {
                transform: translate(85vw, 70vh) rotate(-15deg) scale(1.1);
                opacity: 0.04;
            }
            37.5% {
                transform: translate(70vw, 85vh) rotate(0deg) scale(0.9);
                opacity: 0.06;
            }
            50% {
                transform: translate(15vw, 80vh) rotate(15deg) scale(1.2);
                opacity: 0.03;
            }
            62.5% {
                transform: translate(5vw, 60vh) rotate(30deg) scale(0.7);
                opacity: 0.05;
            }
            75% {
                transform: translate(20vw, 20vh) rotate(45deg) scale(1);
                opacity: 0.04;
            }
            87.5% {
                transform: translate(60vw, 10vh) rotate(60deg) scale(0.9);
                opacity: 0.06;
            }
            100% {
                transform: translate(10vw, 10vh) rotate(-45deg) scale(0.8);
                opacity: 0.03;
            }
        }

        /* تحسينات للهواتف */
        @media (max-width: 768px) {
            #candlestickChart {
                height: 350px !important;
            }

            .card-header .btn {
                font-size: 0.8rem;
                padding: 5px 10px;
            }
        }

        @media (max-width: 480px) {
            #candlestickChart {
                height: 300px !important;
            }

            .card-header h5 {
                font-size: 1rem;
            }

            .card-header .btn {
                font-size: 0.7rem;
                padding: 4px 8px;
            }
        }

        /* تحسينات إضافية للمحاكي */
        .live-price-indicator {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
            100% {
                opacity: 1;
            }
        }
    </style>
</head>

<body>
    <!-- العلامة المائية المتحركة -->
    <div class="watermark">فوركس الأردن</div>

    <!-- علامات مائية إضافية للتأثير -->
    <div class="watermark" style="animation-delay: -5s; opacity: 0.02;">فوركس الأردن</div>
    <div class="watermark" style="animation-delay: -10s; opacity: 0.015;">فوركس الأردن</div>

    <!-- الهيدر الموحد -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../fxjordan.html">
                <i class="fas fa-chart-line" style="margin-left: 8px;"></i>
                فوركس الأردن
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                style="border: 1px solid rgba(100, 255, 218, 0.3); padding: 8px 12px;">
                <i class="fas fa-bars" style="color: #64ffda; font-size: 1.2rem;"></i>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto" style="font-weight: 500;">
                    <li class="nav-item">
                        <a class="nav-link" href="../fxjordan.html#home"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../fxjordan.html#about"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">من
                            نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../fxjordan.html#services"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">
                            خدماتنا</a>
                    </li>

                    <!-- قائمة الأدوات المنسدلة -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="toolsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                            style="color: #64ffda; margin: 0 10px; position: relative; padding: 8px 15px !important; display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-tools" style="font-size: 0.9rem;"></i>
                            الأدوات
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="toolsDropdown">
                            <li>
                                <a class="dropdown-item" href="Risk-management.html"
                                    style="color: #ccd6f6; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px;">
                                    <i class="fas fa-chart-pie" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    خطط إدارة رأس المال
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="Lotcalculator.html"
                                    style="color: #ccd6f6; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px;">
                                    <i class="fas fa-calculator" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    حاسبة اللوت
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="Economic-news.html"
                                    style="color: #ccd6f6; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px;">
                                    <i class="fas fa-calendar-alt" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    التقويم الاقتصادي
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="TradeSimulator.html"
                                    style="color: #64ffda; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px; background: rgba(100, 255, 218, 0.1);">
                                    <i class="fas fa-chart-line" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    محاكي الصفقات
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="../fxjordan.html#plans"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">باقات
                            الاشتراك</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../fxjordan.html#partners"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">الشركاء</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../fxjordan.html#contact"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">
                            تواصل معنا
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid" style="padding-top: 120px; padding-bottom: 50px; position: relative; z-index: 5;">
        <div class="row">
            <!-- لوحة التحكم -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header text-center">
                        <h4 class="mb-0" style="color: var(--accent-color); font-weight: 700;">
                            <i class="fas fa-gamepad" style="margin-left: 10px;"></i>
                            محاكي التداول الوهمي
                        </h4>
                        <p class="mb-0 mt-2" style="color: #ccd6f6; font-size: 0.9rem;">
                            جرب صفقاتك بأمان قبل التداول الحقيقي
                        </p>
                    </div>

                    <div class="card-body">
                        <!-- معلومات الحساب -->
                        <div class="mb-4 p-3" style="background: rgba(100, 255, 218, 0.1); border-radius: 10px; border: 1px solid rgba(100, 255, 218, 0.3);">
                            <div class="row text-center">
                                <div class="col-6">
                                    <h6 style="color: var(--accent-color); margin-bottom: 5px;">الرصيد الحالي</h6>
                                    <h4 id="currentBalance" style="color: var(--main-text); font-weight: 700;">$1,000.00</h4>
                                </div>
                                <div class="col-6">
                                    <h6 style="color: var(--accent-color); margin-bottom: 5px;">إجمالي الربح/الخسارة</h6>
                                    <h4 id="totalPnL" style="color: var(--main-text); font-weight: 700;">$0.00</h4>
                                </div>
                            </div>
                        </div>

                        <!-- نموذج الصفقة -->
                        <form id="tradeForm">
                            <div class="row">
                                <!-- رأس المال الابتدائي -->
                                <div class="col-12 mb-3">
                                    <label for="initialCapital" class="form-label">
                                        <i class="fas fa-dollar-sign" style="color: var(--accent-color); margin-left: 5px;"></i>
                                        رأس المال الابتدائي ($)
                                    </label>
                                    <input type="number" class="form-control" id="initialCapital"
                                           value="1000" min="100" step="0.01" required>
                                </div>

                                <!-- زوج العملة -->
                                <div class="col-12 mb-3">
                                    <label for="symbol" class="form-label">
                                        <i class="fas fa-coins" style="color: var(--accent-color); margin-left: 5px;"></i>
                                        زوج العملة/الأداة
                                    </label>
                                    <select class="form-select" id="symbol" required>
                                        <option value="XAUUSD">الذهب (XAUUSD)</option>
                                        <option value="EURUSD">اليورو/الدولار (EURUSD)</option>
                                        <option value="GBPUSD">الجنيه/الدولار (GBPUSD)</option>
                                        <option value="USDJPY">الدولار/الين (USDJPY)</option>
                                        <option value="USDCHF">الدولار/الفرنك (USDCHF)</option>
                                    </select>
                                </div>

                                <!-- نوع الصفقة -->
                                <div class="col-12 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-exchange-alt" style="color: var(--accent-color); margin-left: 5px;"></i>
                                        نوع الصفقة
                                    </label>
                                    <div class="row">
                                        <div class="col-6">
                                            <button type="button" class="btn btn-success w-100" id="buyBtn">
                                                <i class="fas fa-arrow-up"></i> شراء (Buy)
                                            </button>
                                        </div>
                                        <div class="col-6">
                                            <button type="button" class="btn btn-danger w-100" id="sellBtn">
                                                <i class="fas fa-arrow-down"></i> بيع (Sell)
                                            </button>
                                        </div>
                                    </div>
                                    <input type="hidden" id="tradeType" value="buy">
                                </div>

                                <!-- نقطة الدخول -->
                                <div class="col-6 mb-3">
                                    <label for="entryPrice" class="form-label">
                                        <i class="fas fa-sign-in-alt" style="color: var(--accent-color); margin-left: 5px;"></i>
                                        نقطة الدخول
                                    </label>
                                    <input type="number" class="form-control" id="entryPrice"
                                           placeholder="2000.50" step="0.01" required>
                                </div>

                                <!-- حجم اللوت -->
                                <div class="col-6 mb-3">
                                    <label for="lotSize" class="form-label">
                                        <i class="fas fa-weight" style="color: var(--accent-color); margin-left: 5px;"></i>
                                        حجم اللوت
                                    </label>
                                    <input type="number" class="form-control" id="lotSize"
                                           value="0.10" min="0.01" max="2.00" step="0.01" required>
                                </div>

                                <!-- وقف الخسارة -->
                                <div class="col-6 mb-3">
                                    <label for="stopLoss" class="form-label">
                                        <i class="fas fa-stop-circle" style="color: var(--danger-color); margin-left: 5px;"></i>
                                        وقف الخسارة
                                    </label>
                                    <input type="number" class="form-control" id="stopLoss"
                                           placeholder="1980.00" step="0.01" required>
                                </div>

                                <!-- الهدف -->
                                <div class="col-6 mb-3">
                                    <label for="takeProfit" class="form-label">
                                        <i class="fas fa-bullseye" style="color: var(--success-color); margin-left: 5px;"></i>
                                        الهدف
                                    </label>
                                    <input type="number" class="form-control" id="takeProfit"
                                           placeholder="2020.00" step="0.01" required>
                                </div>

                                <!-- سرعة المحاكاة -->
                                <div class="col-12 mb-4">
                                    <label for="simulationSpeed" class="form-label">
                                        <i class="fas fa-tachometer-alt" style="color: var(--accent-color); margin-left: 5px;"></i>
                                        سرعة المحاكاة
                                    </label>
                                    <select class="form-select" id="simulationSpeed">
                                        <option value="slow">بطيئة (3 ثوان/نقطة)</option>
                                        <option value="normal" selected>عادية (1 ثانية/نقطة)</option>
                                        <option value="fast">سريعة (0.3 ثانية/نقطة)</option>
                                    </select>
                                </div>

                                <!-- أزرار التحكم -->
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary w-100 mb-2" id="startSimulation">
                                        <i class="fas fa-play" style="margin-left: 8px;"></i>
                                        ابدأ المحاكاة
                                    </button>
                                    <button type="button" class="btn btn-secondary w-100" id="resetAccount">
                                        <i class="fas fa-redo" style="margin-left: 8px;"></i>
                                        إعادة تعيين الحساب
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- الرسم البياني ومعلومات الصفقة -->
            <div class="col-lg-8">
                <!-- معلومات الصفقة الحالية -->
                <div class="card mb-4" id="currentTradeInfo" style="display: none;">
                    <div class="card-header">
                        <h5 class="mb-0" style="color: var(--accent-color);">
                            <i class="fas fa-chart-area" style="margin-left: 8px;"></i>
                            الصفقة الحالية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-2">
                                <h6 style="color: var(--secondary-text);">الزوج</h6>
                                <span id="currentSymbol" style="color: var(--main-text); font-weight: 600;">-</span>
                            </div>
                            <div class="col-md-2">
                                <h6 style="color: var(--secondary-text);">النوع</h6>
                                <span id="currentType" style="color: var(--main-text); font-weight: 600;">-</span>
                            </div>
                            <div class="col-md-2">
                                <h6 style="color: var(--secondary-text);">اللوت</h6>
                                <span id="currentLot" style="color: var(--main-text); font-weight: 600;">-</span>
                            </div>
                            <div class="col-md-2">
                                <h6 style="color: var(--secondary-text);">السعر الحالي</h6>
                                <span id="currentPrice" style="color: var(--accent-color); font-weight: 700; font-size: 1.1rem;">-</span>
                            </div>
                            <div class="col-md-2">
                                <h6 style="color: var(--secondary-text);">الربح/الخسارة</h6>
                                <span id="currentPnL" style="font-weight: 700; font-size: 1.1rem;">$0.00</span>
                            </div>
                            <div class="col-md-2">
                                <h6 style="color: var(--secondary-text);">الحالة</h6>
                                <span id="tradeStatus" style="color: var(--accent-color); font-weight: 600;">جاري...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسم البياني بالشموع اليابانية -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" style="color: var(--accent-color);">
                            <i class="fas fa-chart-candlestick" style="margin-left: 8px;"></i>
                            الرسم البياني المباشر - شموع يابانية
                        </h5>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" id="addTradeLines">
                                <i class="fas fa-crosshairs"></i> إضافة خطوط الصفقة
                            </button>
                            <button class="btn btn-sm btn-outline-success" id="getCurrentPrice">
                                <i class="fas fa-sync"></i> السعر الحالي
                            </button>
                            <button class="btn btn-sm btn-outline-warning" id="resetChart">
                                <i class="fas fa-redo"></i> إعادة تعيين
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- الرسم البياني -->
                        <div style="height: 500px; padding: 20px;">
                            <canvas id="candlestickChart"></canvas>
                        </div>

                        <!-- معلومات السعر المباشر -->
                        <div class="p-3" style="background: rgba(100, 255, 218, 0.05); border-top: 1px solid var(--border-color);">
                            <div class="row text-center">
                                <div class="col-md-2">
                                    <h6 style="color: var(--secondary-text); margin-bottom: 5px;">السعر المباشر</h6>
                                    <h5 id="livePrice" style="color: var(--accent-color); font-weight: 700;">-</h5>
                                </div>
                                <div class="col-md-2">
                                    <h6 style="color: var(--secondary-text); margin-bottom: 5px;">التغيير</h6>
                                    <h5 id="priceChange" style="font-weight: 700;">-</h5>
                                </div>
                                <div class="col-md-2">
                                    <h6 style="color: var(--secondary-text); margin-bottom: 5px;">أعلى سعر</h6>
                                    <h5 id="highPrice" style="color: var(--main-text);">-</h5>
                                </div>
                                <div class="col-md-2">
                                    <h6 style="color: var(--secondary-text); margin-bottom: 5px;">أقل سعر</h6>
                                    <h5 id="lowPrice" style="color: var(--main-text);">-</h5>
                                </div>
                                <div class="col-md-2">
                                    <h6 style="color: var(--secondary-text); margin-bottom: 5px;">آخر تحديث</h6>
                                    <h6 id="lastUpdate" style="color: var(--main-text); font-size: 0.8rem;">-</h6>
                                </div>
                                <div class="col-md-2">
                                    <h6 style="color: var(--secondary-text); margin-bottom: 5px;">حالة الاتصال</h6>
                                    <h6 id="connectionStatus" style="font-weight: 600;">
                                        <i class="fas fa-spinner fa-spin" style="color: var(--accent-color);"></i> جاري التحميل...
                                    </h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- سجل الصفقات -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" style="color: var(--accent-color);">
                            <i class="fas fa-history" style="margin-left: 8px;"></i>
                            سجل الصفقات
                        </h5>
                        <button class="btn btn-sm btn-outline-danger" id="clearHistory">
                            <i class="fas fa-trash"></i> مسح السجل
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-striped">
                                <thead>
                                    <tr style="color: var(--accent-color);">
                                        <th>الوقت</th>
                                        <th>الزوج</th>
                                        <th>النوع</th>
                                        <th>اللوت</th>
                                        <th>الدخول</th>
                                        <th>الخروج</th>
                                        <th>النقاط</th>
                                        <th>الربح/الخسارة</th>
                                        <th>النتيجة</th>
                                    </tr>
                                </thead>
                                <tbody id="tradesHistory">
                                    <tr>
                                        <td colspan="9" class="text-center" style="color: var(--secondary-text); padding: 40px;">
                                            <i class="fas fa-chart-line fa-3x mb-3" style="opacity: 0.3;"></i><br>
                                            لا توجد صفقات بعد. ابدأ أول صفقة لك!
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js مع إضافة الشموع اليابانية -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <!-- مكتبة الشموع اليابانية -->
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial"></script>

    <script>
        // متغيرات عامة
        let currentBalance = 1000;
        let totalPnL = 0;
        let isSimulationRunning = false;
        let currentTrade = null;
        let candlestickChart = null;
        let priceUpdateInterval = null;
        let currentLivePrice = 0;
        let candlestickData = [];
        let tradeLines = [];

        // بيانات الشموع اليابانية
        let chartTimeframe = 1; // دقيقة واحدة
        let lastCandleTime = null;
        let lastRealPrices = {};

        // APIs للأسعار الحقيقية
        const FOREX_API_KEY = 'YOUR_API_KEY'; // يمكن الحصول عليه مجاناً من fxratesapi.com
        const GOLD_API_URL = 'https://api.metals.live/v1/spot/gold';
        const FOREX_API_URL = 'https://api.fxratesapi.com/latest';
        const BACKUP_API_URL = 'https://api.exchangerate-api.com/v4/latest/USD';

        // إعداد الصفحة عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            loadAccountData();
            setupEventListeners();
            initializeCandlestickChart();
            startLivePriceUpdates();
        });

        /**
         * تهيئة التطبيق
         */
        function initializeApp() {
            updateBalanceDisplay();
            loadTradeHistory();

            // إعداد القائمة المنسدلة للأدوات
            const toolsDropdown = document.getElementById('toolsDropdown');
            const dropdownMenu = toolsDropdown?.nextElementSibling;

            if (toolsDropdown && dropdownMenu) {
                toolsDropdown.parentElement.addEventListener('mouseenter', function() {
                    dropdownMenu.classList.add('show');
                });

                toolsDropdown.parentElement.addEventListener('mouseleave', function() {
                    dropdownMenu.classList.remove('show');
                });

                dropdownMenu.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
        }

        /**
         * إعداد مستمعي الأحداث
         */
        function setupEventListeners() {
            // أزرار نوع الصفقة
            document.getElementById('buyBtn').addEventListener('click', function() {
                selectTradeType('buy');
            });

            document.getElementById('sellBtn').addEventListener('click', function() {
                selectTradeType('sell');
            });

            // نموذج الصفقة
            document.getElementById('tradeForm').addEventListener('submit', function(e) {
                e.preventDefault();
                startTradeSimulation();
            });

            // إعادة تعيين الحساب
            document.getElementById('resetAccount').addEventListener('click', function() {
                resetAccount();
            });

            // مسح السجل
            document.getElementById('clearHistory').addEventListener('click', function() {
                clearTradeHistory();
            });

            // تحديث رأس المال
            document.getElementById('initialCapital').addEventListener('change', function() {
                const newBalance = parseFloat(this.value) || 1000;
                currentBalance = newBalance;
                totalPnL = 0;
                updateBalanceDisplay();
                saveAccountData();
            });

            // تحديث السعر عند تغيير الأداة
            document.getElementById('symbol').addEventListener('change', function() {
                resetCandlestickData();
                updateEntryPrice();
            });

            // أزرار الرسم البياني
            document.getElementById('getCurrentPrice').addEventListener('click', function() {
                getCurrentPriceFromChart();
            });

            document.getElementById('addTradeLines').addEventListener('click', function() {
                addTradeLinestoChart();
            });

            document.getElementById('resetChart').addEventListener('click', function() {
                resetCandlestickChart();
            });
        }

        /**
         * اختيار نوع الصفقة
         */
        function selectTradeType(type) {
            document.getElementById('tradeType').value = type;

            const buyBtn = document.getElementById('buyBtn');
            const sellBtn = document.getElementById('sellBtn');

            if (type === 'buy') {
                buyBtn.classList.remove('btn-outline-success');
                buyBtn.classList.add('btn-success');
                sellBtn.classList.remove('btn-danger');
                sellBtn.classList.add('btn-outline-danger');
            } else {
                sellBtn.classList.remove('btn-outline-danger');
                sellBtn.classList.add('btn-danger');
                buyBtn.classList.remove('btn-success');
                buyBtn.classList.add('btn-outline-success');
            }
        }

        /**
         * تهيئة الرسم البياني بالشموع اليابانية
         */
        function initializeCandlestickChart() {
            const ctx = document.getElementById('candlestickChart').getContext('2d');

            candlestickChart = new Chart(ctx, {
                type: 'candlestick',
                data: {
                    datasets: [{
                        label: 'السعر',
                        data: candlestickData,
                        borderColor: '#64ffda',
                        backgroundColor: 'rgba(100, 255, 218, 0.1)',
                        borderWidth: 1,
                        color: {
                            up: '#4CAF50',
                            down: '#f44336',
                            unchanged: '#8892b0'
                        },
                        borderColor: {
                            up: '#4CAF50',
                            down: '#f44336',
                            unchanged: '#8892b0'
                        }
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            labels: {
                                color: '#e6f1ff',
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(10, 25, 47, 0.9)',
                            titleColor: '#64ffda',
                            bodyColor: '#e6f1ff',
                            borderColor: '#64ffda',
                            borderWidth: 1,
                            callbacks: {
                                title: function(context) {
                                    return new Date(context[0].parsed.x).toLocaleString('ar-SA');
                                },
                                label: function(context) {
                                    const data = context.parsed;
                                    return [
                                        `الافتتاح: ${data.o.toFixed(4)}`,
                                        `الأعلى: ${data.h.toFixed(4)}`,
                                        `الأقل: ${data.l.toFixed(4)}`,
                                        `الإغلاق: ${data.c.toFixed(4)}`
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'minute',
                                displayFormats: {
                                    minute: 'HH:mm'
                                }
                            },
                            ticks: {
                                color: '#8892b0',
                                font: {
                                    family: 'Cairo'
                                }
                            },
                            grid: {
                                color: 'rgba(100, 255, 218, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#8892b0',
                                font: {
                                    family: 'Cairo'
                                },
                                callback: function(value) {
                                    return value.toFixed(4);
                                }
                            },
                            grid: {
                                color: 'rgba(100, 255, 218, 0.1)'
                            }
                        }
                    }
                }
            });

            // إنشاء بيانات أولية
            generateInitialCandlestickData();
        }

        /**
         * إنشاء بيانات شموع أولية
         */
        async function generateInitialCandlestickData() {
            const symbol = document.getElementById('symbol').value;
            const now = new Date();
            candlestickData = [];

            // محاولة جلب السعر الحقيقي أولاً
            let basePrice = 2000.50; // افتراضي للذهب

            try {
                if (symbol === 'XAUUSD') {
                    const goldData = await fetchGoldPrice();
                    basePrice = goldData.price;
                } else {
                    const forexData = await fetchForexPrice(symbol);
                    basePrice = forexData.price;
                }
            } catch (error) {
                console.log('استخدام السعر الافتراضي:', error);
                const defaultPrices = {
                    'XAUUSD': 2000.50,
                    'EURUSD': 1.0850,
                    'GBPUSD': 1.2650,
                    'USDJPY': 149.50,
                    'USDCHF': 0.8950
                };
                basePrice = defaultPrices[symbol];
            }

            // إنشاء 20 شمعة أولية بناءً على السعر الحقيقي
            for (let i = 19; i >= 0; i--) {
                const time = new Date(now.getTime() - (i * 60000)); // كل دقيقة
                const priceVariation = (Math.random() - 0.5) * (basePrice * 0.001); // تذبذب 0.1%
                const open = basePrice + priceVariation;
                const volatility = Math.random() * (basePrice * 0.0005); // تذبذب أصغر
                const high = open + volatility;
                const low = open - volatility;
                const close = low + Math.random() * (high - low);

                candlestickData.push({
                    x: time.getTime(),
                    o: open,
                    h: high,
                    l: low,
                    c: close
                });

                basePrice = close; // استخدام الإغلاق كأساس للشمعة التالية
            }

            lastCandleTime = now;
            currentLivePrice = basePrice;
            updateCandlestickChart();
        }

        /**
         * تحديث الرسم البياني بالشموع
         */
        function updateCandlestickChart() {
            if (candlestickChart) {
                candlestickChart.data.datasets[0].data = candlestickData;
                candlestickChart.update('none');
            }
        }

        /**
         * إضافة شمعة جديدة من السعر الحقيقي
         */
        function addNewCandleFromRealPrice() {
            const now = new Date();
            const symbol = document.getElementById('symbol').value;

            if (candlestickData.length > 0 && lastRealPrices[symbol]) {
                const lastCandle = candlestickData[candlestickData.length - 1];
                const realPrice = lastRealPrices[symbol];

                // استخدام السعر الحقيقي مع تذبذب صغير للشمعة
                const open = lastCandle.c;
                const close = realPrice.price;
                const volatility = Math.abs(close - open) * 0.3; // تذبذب 30% من الحركة

                const high = Math.max(open, close) + volatility;
                const low = Math.min(open, close) - volatility;

                const newCandle = {
                    x: now.getTime(),
                    o: open,
                    h: high,
                    l: low,
                    c: close
                };

                candlestickData.push(newCandle);
                currentLivePrice = close;

                // الاحتفاظ بآخر 50 شمعة فقط
                if (candlestickData.length > 50) {
                    candlestickData.shift();
                }

                updateCandlestickChart();
            } else {
                // إذا لم يكن هناك سعر حقيقي، استخدم الطريقة القديمة
                addNewCandle();
            }
        }

        /**
         * إضافة شمعة جديدة (الطريقة القديمة للاحتياط)
         */
        function addNewCandle() {
            const now = new Date();
            const symbol = document.getElementById('symbol').value;

            if (candlestickData.length > 0) {
                const lastCandle = candlestickData[candlestickData.length - 1];
                const open = lastCandle.c;
                const volatility = Math.random() * 1.5;
                const direction = Math.random() > 0.5 ? 1 : -1;
                const change = direction * volatility;

                const high = Math.max(open, open + change) + Math.random() * 0.5;
                const low = Math.min(open, open + change) - Math.random() * 0.5;
                const close = open + change;

                const newCandle = {
                    x: now.getTime(),
                    o: open,
                    h: high,
                    l: low,
                    c: close
                };

                candlestickData.push(newCandle);
                currentLivePrice = close;

                // الاحتفاظ بآخر 50 شمعة فقط
                if (candlestickData.length > 50) {
                    candlestickData.shift();
                }

                updateCandlestickChart();
            }
        }

        /**
         * إعادة تعيين بيانات الشموع
         */
        function resetCandlestickData() {
            candlestickData = [];
            generateInitialCandlestickData();
        }

        /**
         * إعادة تعيين الرسم البياني
         */
        function resetCandlestickChart() {
            resetCandlestickData();
            showNotification('تم إعادة تعيين الرسم البياني', 'success');
        }

        /**
         * الحصول على السعر الحالي من الرسم البياني
         */
        function getCurrentPriceFromChart() {
            if (candlestickData.length > 0) {
                const lastCandle = candlestickData[candlestickData.length - 1];
                const currentPrice = lastCandle.c;
                document.getElementById('entryPrice').value = currentPrice.toFixed(4);
                showNotification('تم تحديث سعر الدخول للسعر الحالي', 'success');
            } else {
                updateEntryPrice();
            }
        }

        /**
         * إضافة خطوط الصفقة للرسم البياني
         */
        function addTradeLinestoChart() {
            const entryPrice = parseFloat(document.getElementById('entryPrice').value);
            const stopLoss = parseFloat(document.getElementById('stopLoss').value);
            const takeProfit = parseFloat(document.getElementById('takeProfit').value);

            if (!entryPrice || !stopLoss || !takeProfit) {
                showNotification('يرجى إدخال جميع الأسعار أولاً', 'warning');
                return;
            }

            // إضافة خطوط أفقية للرسم البياني
            if (candlestickChart) {
                // إزالة الخطوط السابقة
                candlestickChart.options.plugins.annotation = {
                    annotations: {
                        entryLine: {
                            type: 'line',
                            yMin: entryPrice,
                            yMax: entryPrice,
                            borderColor: '#64ffda',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            label: {
                                content: `دخول: ${entryPrice}`,
                                enabled: true,
                                position: 'end',
                                backgroundColor: '#64ffda',
                                color: '#0a192f'
                            }
                        },
                        stopLine: {
                            type: 'line',
                            yMin: stopLoss,
                            yMax: stopLoss,
                            borderColor: '#f44336',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            label: {
                                content: `ستوب: ${stopLoss}`,
                                enabled: true,
                                position: 'end',
                                backgroundColor: '#f44336',
                                color: '#ffffff'
                            }
                        },
                        profitLine: {
                            type: 'line',
                            yMin: takeProfit,
                            yMax: takeProfit,
                            borderColor: '#4CAF50',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            label: {
                                content: `هدف: ${takeProfit}`,
                                enabled: true,
                                position: 'end',
                                backgroundColor: '#4CAF50',
                                color: '#ffffff'
                            }
                        }
                    }
                };

                candlestickChart.update();
            }

            showNotification('تم إضافة خطوط الصفقة للرسم البياني', 'success');
        }



        /**
         * بدء محاكاة الصفقة
         */
        function startTradeSimulation() {
            if (isSimulationRunning) {
                alert('يوجد محاكاة قيد التشغيل بالفعل!');
                return;
            }

            // جمع بيانات الصفقة
            const tradeData = {
                symbol: document.getElementById('symbol').value,
                type: document.getElementById('tradeType').value,
                entryPrice: parseFloat(document.getElementById('entryPrice').value),
                lotSize: parseFloat(document.getElementById('lotSize').value),
                stopLoss: parseFloat(document.getElementById('stopLoss').value),
                takeProfit: parseFloat(document.getElementById('takeProfit').value),
                speed: document.getElementById('simulationSpeed').value
            };

            // التحقق من صحة البيانات
            if (!validateTradeData(tradeData)) {
                return;
            }

            // بدء المحاكاة
            currentTrade = {
                ...tradeData,
                startTime: new Date(),
                currentPrice: tradeData.entryPrice,
                pnl: 0,
                status: 'running'
            };

            isSimulationRunning = true;
            updateCurrentTradeDisplay();
            document.getElementById('currentTradeInfo').style.display = 'block';
            document.getElementById('startSimulation').disabled = true;
            document.getElementById('startSimulation').innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المحاكاة...';

            // بدء حركة السعر
            startPriceMovement();
        }

        /**
         * التحقق من صحة بيانات الصفقة
         */
        function validateTradeData(data) {
            if (data.lotSize < 0.01 || data.lotSize > 2.00) {
                alert('حجم اللوت يجب أن يكون بين 0.01 و 2.00');
                return false;
            }

            if (data.type === 'buy') {
                if (data.stopLoss >= data.entryPrice) {
                    alert('في صفقة الشراء، وقف الخسارة يجب أن يكون أقل من سعر الدخول');
                    return false;
                }
                if (data.takeProfit <= data.entryPrice) {
                    alert('في صفقة الشراء، الهدف يجب أن يكون أعلى من سعر الدخول');
                    return false;
                }
            } else {
                if (data.stopLoss <= data.entryPrice) {
                    alert('في صفقة البيع، وقف الخسارة يجب أن يكون أعلى من سعر الدخول');
                    return false;
                }
                if (data.takeProfit >= data.entryPrice) {
                    alert('في صفقة البيع، الهدف يجب أن يكون أقل من سعر الدخول');
                    return false;
                }
            }

            return true;
        }

        /**
         * بدء تحديثات الأسعار المباشرة
         */
        function startLivePriceUpdates() {
            // تحديث الأسعار الحقيقية كل 10 ثوان
            priceUpdateInterval = setInterval(() => {
                fetchRealPrices();
            }, 10000);

            // تحديث فوري عند التحميل
            fetchRealPrices();

            // تحديث الشموع كل 30 ثانية
            setInterval(() => {
                addNewCandleFromRealPrice();
            }, 30000);
        }

        /**
         * جلب الأسعار الحقيقية من APIs
         */
        async function fetchRealPrices() {
            const symbol = document.getElementById('symbol').value;

            // إظهار مؤشر التحميل
            updateConnectionStatus('loading');

            try {
                let realPrice = null;

                if (symbol === 'XAUUSD') {
                    // جلب سعر الذهب الحقيقي
                    realPrice = await fetchGoldPrice();
                } else {
                    // جلب أسعار العملات الحقيقية
                    realPrice = await fetchForexPrice(symbol);
                }

                if (realPrice) {
                    lastRealPrices[symbol] = realPrice;
                    currentLivePrice = realPrice.price;
                    updateLivePriceDisplay(realPrice, symbol);

                    // تحديث سعر الدخول إذا لم تكن هناك صفقة جارية
                    if (!isSimulationRunning) {
                        updateEntryPrice();
                    }

                    console.log(`تم تحديث سعر ${symbol}: ${realPrice.price.toFixed(4)}`);
                } else {
                    throw new Error('لم يتم الحصول على بيانات صحيحة');
                }

            } catch (error) {
                console.log('خطأ في جلب الأسعار الحقيقية:', error);
                updateConnectionStatus(false);
                showNotification('تعذر جلب الأسعار المباشرة، سيتم المحاولة مرة أخرى', 'warning');

                // محاولة مرة أخرى بعد 30 ثانية
                setTimeout(() => {
                    fetchRealPrices();
                }, 30000);
            }
        }

        /**
         * جلب سعر الذهب الحقيقي
         */
        async function fetchGoldPrice() {
            // قائمة APIs للذهب
            const goldAPIs = [
                {
                    url: 'https://api.metals.live/v1/spot/gold',
                    parser: (data) => ({
                        price: data[0].price,
                        change: data[0].ch || 0,
                        changePercent: data[0].chp || 0,
                        high: data[0].price + Math.abs(data[0].ch || 1),
                        low: data[0].price - Math.abs(data[0].ch || 1)
                    })
                },
                {
                    url: 'https://api.coindesk.com/v1/bpi/currentprice.json',
                    parser: (data) => {
                        // تحويل تقريبي من البيتكوين للذهب (للاختبار)
                        const btcPrice = parseFloat(data.bpi.USD.rate.replace(',', ''));
                        const goldPrice = btcPrice / 20; // تقريب
                        return {
                            price: goldPrice,
                            change: 0,
                            changePercent: 0,
                            high: goldPrice + 1,
                            low: goldPrice - 1
                        };
                    }
                }
            ];

            // محاولة كل API
            for (const api of goldAPIs) {
                try {
                    const response = await fetch(api.url, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        return api.parser(data);
                    }
                } catch (error) {
                    console.log(`خطأ في API: ${api.url}`, error);
                    continue;
                }
            }

            // في حالة فشل جميع APIs، استخدم سعر افتراضي
            console.log('استخدام سعر الذهب الافتراضي');
            return {
                price: 2000.50,
                change: 0,
                changePercent: 0,
                high: 2001.00,
                low: 2000.00
            };
        }

        /**
         * جلب أسعار العملات الحقيقية
         */
        async function fetchForexPrice(symbol) {
            try {
                // تحويل الرمز إلى صيغة API
                const baseCurrency = symbol.substring(0, 3);
                const quoteCurrency = symbol.substring(3, 6);

                // استخدام API مجاني للعملات
                const response = await fetch(`https://api.exchangerate-api.com/v4/latest/${baseCurrency}`);

                if (response.ok) {
                    const data = await response.json();
                    const rate = data.rates[quoteCurrency];

                    if (rate) {
                        // حساب تغيير تقريبي (محاكاة)
                        const change = (Math.random() - 0.5) * 0.001;
                        const changePercent = (change / rate) * 100;

                        return {
                            price: rate,
                            change: change,
                            changePercent: changePercent,
                            high: rate + Math.abs(change),
                            low: rate - Math.abs(change)
                        };
                    }
                }
            } catch (error) {
                console.log('خطأ في جلب سعر العملة:', error);
            }

            // أسعار افتراضية في حالة الفشل
            const defaultPrices = {
                'EURUSD': 1.0850,
                'GBPUSD': 1.2650,
                'USDJPY': 149.50,
                'USDCHF': 0.8950
            };

            const price = defaultPrices[symbol] || 1.0000;
            return {
                price: price,
                change: 0,
                changePercent: 0,
                high: price + 0.001,
                low: price - 0.001
            };
        }

        /**
         * تحديث عرض الأسعار المباشرة
         */
        function updateLivePriceDisplay(priceData, symbol) {
            const changeText = priceData.change >= 0 ?
                `+${priceData.change.toFixed(4)} (+${priceData.changePercent.toFixed(2)}%)` :
                `${priceData.change.toFixed(4)} (${priceData.changePercent.toFixed(2)}%)`;

            // تحديث العرض
            document.getElementById('livePrice').textContent = priceData.price.toFixed(symbol === 'USDJPY' ? 2 : 4);
            document.getElementById('priceChange').textContent = changeText;
            document.getElementById('priceChange').style.color = priceData.change >= 0 ? 'var(--success-color)' : 'var(--danger-color)';
            document.getElementById('highPrice').textContent = priceData.high.toFixed(symbol === 'USDJPY' ? 2 : 4);
            document.getElementById('lowPrice').textContent = priceData.low.toFixed(symbol === 'USDJPY' ? 2 : 4);

            // تحديث آخر تحديث
            const now = new Date();
            document.getElementById('lastUpdate').textContent = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            // تحديث حالة الاتصال
            updateConnectionStatus(true);
        }

        /**
         * تحديث حالة الاتصال
         */
        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connectionStatus');

            if (status === 'loading') {
                statusElement.innerHTML = '<i class="fas fa-spinner fa-spin" style="color: var(--accent-color);"></i> جاري التحديث...';
            } else if (status === true) {
                statusElement.innerHTML = '<i class="fas fa-circle" style="color: var(--success-color);"></i> متصل';
            } else {
                statusElement.innerHTML = '<i class="fas fa-circle" style="color: var(--danger-color);"></i> منقطع';
            }
        }

        /**
         * تحديث سعر الدخول حسب الأداة المختارة
         */
        function updateEntryPrice() {
            if (candlestickData.length > 0) {
                const lastCandle = candlestickData[candlestickData.length - 1];
                const symbol = document.getElementById('symbol').value;
                document.getElementById('entryPrice').value = lastCandle.c.toFixed(symbol === 'USDJPY' ? 2 : 4);
            } else {
                // أسعار افتراضية إذا لم تكن هناك بيانات
                const defaultPrices = {
                    'XAUUSD': 2000.50,
                    'EURUSD': 1.0850,
                    'GBPUSD': 1.2650,
                    'USDJPY': 149.50,
                    'USDCHF': 0.8950
                };

                const symbol = document.getElementById('symbol').value;
                const price = defaultPrices[symbol] || 1.0000;
                document.getElementById('entryPrice').value = price.toFixed(symbol === 'USDJPY' ? 2 : 4);
            }
        }

        /**
         * بدء محاكاة الصفقة مع الأسعار المباشرة
         */
        function startPriceMovement() {
            const speeds = {
                'slow': 3000,
                'normal': 1000,
                'fast': 300
            };

            const interval = speeds[currentTrade.speed];
            let step = 0;

            simulationInterval = setInterval(() => {
                step++;

                // استخدام السعر من آخر شمعة مع تذبذب إضافي
                if (candlestickData.length > 0) {
                    const lastCandle = candlestickData[candlestickData.length - 1];
                    const priceChange = generatePriceMovement(currentTrade.symbol);
                    currentTrade.currentPrice = lastCandle.c + priceChange;
                } else {
                    // استخدام حركة عشوائية إذا لم تكن هناك بيانات
                    const priceChange = generatePriceMovement(currentTrade.symbol);
                    currentTrade.currentPrice += priceChange;
                }

                // حساب الربح/الخسارة
                calculatePnL();

                // تحديث العرض
                updateCurrentTradeDisplay();

                // فحص إذا ضرب الستوب أو الهدف
                if (checkTradeExit()) {
                    endTradeSimulation();
                }

                // إيقاف المحاكاة بعد 100 خطوة كحد أقصى
                if (step >= 100) {
                    endTradeSimulation('timeout');
                }

            }, interval);
        }

        /**
         * توليد حركة سعر عشوائية
         */
        function generatePriceMovement(symbol) {
            const volatility = {
                'XAUUSD': 2.0,
                'EURUSD': 0.0020,
                'GBPUSD': 0.0025,
                'USDJPY': 0.30,
                'USDCHF': 0.0015
            };

            const vol = volatility[symbol] || 1.0;
            return (Math.random() - 0.5) * vol;
        }

        /**
         * حساب الربح والخسارة
         */
        function calculatePnL() {
            const priceDiff = currentTrade.type === 'buy'
                ? currentTrade.currentPrice - currentTrade.entryPrice
                : currentTrade.entryPrice - currentTrade.currentPrice;

            // حساب عدد النقاط
            const pips = currentTrade.symbol === 'USDJPY'
                ? priceDiff * 100
                : priceDiff * 10000;

            // حساب قيمة النقطة
            let pipValue;
            if (currentTrade.symbol === 'XAUUSD') {
                pipValue = currentTrade.lotSize * 1; // $1 للوت الواحد في الذهب
            } else {
                pipValue = currentTrade.lotSize * 10; // $10 للوت الواحد في العملات
            }

            currentTrade.pnl = pips * pipValue;
            currentTrade.pips = pips;
        }

        /**
         * فحص خروج الصفقة
         */
        function checkTradeExit() {
            if (currentTrade.type === 'buy') {
                if (currentTrade.currentPrice <= currentTrade.stopLoss) {
                    currentTrade.status = 'stop_loss';
                    return true;
                }
                if (currentTrade.currentPrice >= currentTrade.takeProfit) {
                    currentTrade.status = 'take_profit';
                    return true;
                }
            } else {
                if (currentTrade.currentPrice >= currentTrade.stopLoss) {
                    currentTrade.status = 'stop_loss';
                    return true;
                }
                if (currentTrade.currentPrice <= currentTrade.takeProfit) {
                    currentTrade.status = 'take_profit';
                    return true;
                }
            }
            return false;
        }

        /**
         * إنهاء محاكاة الصفقة
         */
        function endTradeSimulation(reason = null) {
            cleanupSimulation();
            isSimulationRunning = false;

            if (reason === 'timeout') {
                currentTrade.status = 'manual_close';
            }

            // تحديث الرصيد
            currentBalance += currentTrade.pnl;
            totalPnL += currentTrade.pnl;

            // حفظ الصفقة في السجل
            saveTradeToHistory();

            // تحديث العرض
            updateBalanceDisplay();
            updateCurrentTradeDisplay();

            // إعادة تفعيل الزر
            document.getElementById('startSimulation').disabled = false;
            document.getElementById('startSimulation').innerHTML = '<i class="fas fa-play"></i> ابدأ المحاكاة';

            // عرض النتيجة
            showTradeResult();

            // حفظ البيانات
            saveAccountData();
        }

        /**
         * عرض نتيجة الصفقة
         */
        function showTradeResult() {
            const isProfit = currentTrade.pnl > 0;
            const resultClass = isProfit ? 'success' : 'danger';
            const resultIcon = isProfit ? 'fa-check-circle' : 'fa-times-circle';
            const resultText = isProfit ? 'ربح' : 'خسارة';

            const statusText = {
                'take_profit': 'تم تحقيق الهدف',
                'stop_loss': 'تم ضرب وقف الخسارة',
                'manual_close': 'إغلاق يدوي'
            };

            alert(`🎯 نتيجة الصفقة:\n\n` +
                  `النوع: ${currentTrade.type === 'buy' ? 'شراء' : 'بيع'}\n` +
                  `الزوج: ${currentTrade.symbol}\n` +
                  `النتيجة: ${resultText}\n` +
                  `المبلغ: $${Math.abs(currentTrade.pnl).toFixed(2)}\n` +
                  `النقاط: ${currentTrade.pips.toFixed(1)}\n` +
                  `السبب: ${statusText[currentTrade.status]}\n\n` +
                  `الرصيد الجديد: $${currentBalance.toFixed(2)}`);
        }

        /**
         * تحديث عرض الرصيد
         */
        function updateBalanceDisplay() {
            document.getElementById('currentBalance').textContent = `$${formatNumber(currentBalance)}`;

            const pnlElement = document.getElementById('totalPnL');
            pnlElement.textContent = `$${formatNumber(totalPnL)}`;
            pnlElement.style.color = totalPnL >= 0 ? 'var(--success-color)' : 'var(--danger-color)';
        }

        /**
         * تحديث عرض الصفقة الحالية
         */
        function updateCurrentTradeDisplay() {
            if (!currentTrade) return;

            document.getElementById('currentSymbol').textContent = currentTrade.symbol;
            document.getElementById('currentType').textContent = currentTrade.type === 'buy' ? 'شراء' : 'بيع';
            document.getElementById('currentLot').textContent = currentTrade.lotSize.toFixed(2);
            document.getElementById('currentPrice').textContent = currentTrade.currentPrice.toFixed(currentTrade.symbol === 'USDJPY' ? 2 : 4);

            const pnlElement = document.getElementById('currentPnL');
            pnlElement.textContent = `$${formatNumber(currentTrade.pnl)}`;
            pnlElement.style.color = currentTrade.pnl >= 0 ? 'var(--success-color)' : 'var(--danger-color)';

            const statusElement = document.getElementById('tradeStatus');
            const statusTexts = {
                'running': 'جاري...',
                'take_profit': 'تم تحقيق الهدف',
                'stop_loss': 'ضرب وقف الخسارة',
                'manual_close': 'إغلاق يدوي'
            };
            statusElement.textContent = statusTexts[currentTrade.status] || 'جاري...';
        }

        /**
         * عرض إشعار
         */
        function showNotification(message, type = 'info') {
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'warning' ? 'alert-warning' :
                              type === 'danger' ? 'alert-danger' : 'alert-info';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // إزالة تلقائية بعد 5 ثوان
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        /**
         * تنظيف الموارد عند إنهاء المحاكاة
         */
        function cleanupSimulation() {
            if (simulationInterval) {
                clearInterval(simulationInterval);
                simulationInterval = null;
            }
        }

        /**
         * حفظ الصفقة في السجل
         */
        function saveTradeToHistory() {
            const trade = {
                id: Date.now(),
                timestamp: currentTrade.startTime,
                symbol: currentTrade.symbol,
                type: currentTrade.type,
                lotSize: currentTrade.lotSize,
                entryPrice: currentTrade.entryPrice,
                exitPrice: currentTrade.currentPrice,
                pips: currentTrade.pips,
                pnl: currentTrade.pnl,
                status: currentTrade.status
            };

            let history = JSON.parse(localStorage.getItem('tradeHistory') || '[]');
            history.unshift(trade); // إضافة في البداية

            // الاحتفاظ بآخر 50 صفقة فقط
            if (history.length > 50) {
                history = history.slice(0, 50);
            }

            localStorage.setItem('tradeHistory', JSON.stringify(history));
            loadTradeHistory();
        }

        /**
         * تحميل سجل الصفقات
         */
        function loadTradeHistory() {
            const history = JSON.parse(localStorage.getItem('tradeHistory') || '[]');
            const tbody = document.getElementById('tradesHistory');

            if (history.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center" style="color: var(--secondary-text); padding: 40px;">
                            <i class="fas fa-chart-line fa-3x mb-3" style="opacity: 0.3;"></i><br>
                            لا توجد صفقات بعد. ابدأ أول صفقة لك!
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = history.map(trade => {
                const isProfit = trade.pnl > 0;
                const resultClass = isProfit ? 'var(--success-color)' : 'var(--danger-color)';
                const resultIcon = isProfit ? 'fa-arrow-up' : 'fa-arrow-down';

                const statusTexts = {
                    'take_profit': 'هدف',
                    'stop_loss': 'ستوب',
                    'manual_close': 'يدوي'
                };

                return `
                    <tr>
                        <td style="color: var(--secondary-text); font-size: 0.85rem;">
                            ${new Date(trade.timestamp).toLocaleString('ar-SA')}
                        </td>
                        <td style="color: var(--accent-color); font-weight: 600;">${trade.symbol}</td>
                        <td>
                            <span class="badge ${trade.type === 'buy' ? 'bg-success' : 'bg-danger'}">
                                ${trade.type === 'buy' ? 'شراء' : 'بيع'}
                            </span>
                        </td>
                        <td style="color: var(--main-text);">${trade.lotSize.toFixed(2)}</td>
                        <td style="color: var(--main-text);">${trade.entryPrice.toFixed(trade.symbol === 'USDJPY' ? 2 : 4)}</td>
                        <td style="color: var(--main-text);">${trade.exitPrice.toFixed(trade.symbol === 'USDJPY' ? 2 : 4)}</td>
                        <td style="color: var(--main-text);">${trade.pips.toFixed(1)}</td>
                        <td style="color: ${resultClass}; font-weight: 600;">
                            <i class="fas ${resultIcon}"></i> $${Math.abs(trade.pnl).toFixed(2)}
                        </td>
                        <td>
                            <span class="badge ${isProfit ? 'bg-success' : 'bg-danger'}">
                                ${statusTexts[trade.status] || trade.status}
                            </span>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        /**
         * مسح سجل الصفقات
         */
        function clearTradeHistory() {
            if (confirm('هل أنت متأكد من مسح جميع الصفقات؟')) {
                localStorage.removeItem('tradeHistory');
                loadTradeHistory();
            }
        }

        /**
         * إعادة تعيين الحساب
         */
        function resetAccount() {
            if (isSimulationRunning) {
                showNotification('لا يمكن إعادة تعيين الحساب أثناء تشغيل المحاكاة', 'warning');
                return;
            }

            if (confirm('هل أنت متأكد من إعادة تعيين الحساب؟ سيتم فقدان جميع البيانات.')) {
                const initialCapital = parseFloat(document.getElementById('initialCapital').value) || 1000;
                currentBalance = initialCapital;
                totalPnL = 0;

                updateBalanceDisplay();
                document.getElementById('currentTradeInfo').style.display = 'none';

                saveAccountData();
                showNotification('تم إعادة تعيين الحساب بنجاح', 'success');
            }
        }

        /**
         * حفظ بيانات الحساب
         */
        function saveAccountData() {
            const accountData = {
                currentBalance: currentBalance,
                totalPnL: totalPnL,
                lastUpdate: new Date().toISOString()
            };
            localStorage.setItem('accountData', JSON.stringify(accountData));
        }

        /**
         * تحميل بيانات الحساب
         */
        function loadAccountData() {
            const accountData = JSON.parse(localStorage.getItem('accountData') || '{}');
            if (accountData.currentBalance) {
                currentBalance = accountData.currentBalance;
                totalPnL = accountData.totalPnL || 0;
                document.getElementById('initialCapital').value = currentBalance.toFixed(2);
            }
        }

        /**
         * تنسيق الأرقام
         */
        function formatNumber(num) {
            return num.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        // تحديث السعر عند تحميل الصفحة
        window.addEventListener('load', function() {
            updateEntryPrice();
        });
    </script>
</body>
</html>
